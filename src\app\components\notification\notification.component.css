/* notification.component.css */
.notification {
    position: relative;
    padding: 16px 20px;
    margin-bottom: 12px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    color: white;
    display: flex;
    align-items: flex-start;
    max-width: 350px;
    animation: slideIn 0.3s ease-out forwards;
    overflow: hidden;
  }
  
  .notification.with-icon {
    padding-left: 56px;
  }
  
  .notification::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background-color: rgba(255, 255, 255, 0.5);
  }
  
  .notification.success {
    background-color: #4CAF50;
  }
  
  .notification.error {
    background-color: #f44336;
  }
  
  .notification.warning {
    background-color: #FF9800;
  }
  
  .notification.info {
    background-color: #2196F3;
  }
  
  .icon {
    position: absolute;
    left: 16px;
    top: 16px;
    font-size: 24px;
  }
  
  .content {
    flex: 1;
  }
  
  .title {
    margin: 0 0 4px 0;
    font-size: 16px;
    font-weight: 500;
  }
  
  .message {
    margin: 0;
    font-size: 14px;
    line-height: 1.4;
  }
  
  .close-btn {
    background: none;
    border: none;
    color: white;
    opacity: 0.7;
    cursor: pointer;
    padding: 0;
    margin-left: 12px;
    display: flex;
    align-items: center;
    transition: opacity 0.2s;
  }
  
  .close-btn:hover {
    opacity: 1;
  }
  
  @keyframes slideIn {
    from {
      transform: translateX(100%);
      opacity: 0;
    }
    to {
      transform: translateX(0);
      opacity: 1;
    }
  }