import { Client } from "./client";
import { Local } from "./local";

export interface Site {
  Id: string;
  Nom: string;        // Changed from 'nom'
  Pays: string;       // Changed from 'pays'
  Ville: string;      // Changed from 'ville'
  Rue: string;        // Changed from 'rue'
  TelephoneSurSite: string;
  Description: string;
  Images: string;     // Changed from 'images'
  ContactDeSite: string;
  IsEnabled: boolean; // Changed from 'isEnabled'
  IdClient: string;
  CreatedAt: string;
}

