import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

export type OrganisationType =
  | 'Ecole'
  | 'Hopital'
  | 'Entrepot'
  | 'Bureau'
  | 'Usine'
  | 'Magasin'
  | 'Residence'
  | 'CentreCommercial'
  | 'Restaurant'
  | 'Hotel'
  | 'Maison';

export interface Site {
  Id: string;
  Nom: string;
  Pays: string;
  Ville: string;
  Rue: string;
  TelephoneSurSite: string;
  Description: string;
  Images: string[] | null;
  ContactDeSite: string;
  IsEnabled: boolean;
  IdClient: string;
  CreatedAt: string;
}
export interface Organisation {
  id: number;
  nom: string;
  type: OrganisationType;
  nombreEmployees: number;
  nombreEquipement: number;
  emailAddress?: string;
  logoPath?: string;
  latitude?: number;
  longitude?: number;
  consommation: number;
  sites?: Site[];
  createdAt?: Date;
  updatedAt?: Date;
  logoImage?: string; // Base64 string for the image
  logoContentType?: string;
}

export interface PaginationInfo {
  totalCount: number;
  totalPages: number;
  currentPage: number;
  pageSize: number;
}

export interface PaginatedResponse<T> {
  items: T[];
  pagination: PaginationInfo;
}

@Injectable({
  providedIn: 'root',
})
export class OrganisationService {
  readonly apiUrl = 'http://************:5544/api/Organisations';
  readonly baseUrl = 'http://************:5544';

  constructor(readonly http: HttpClient) {}

  getOrganisationsPage(
    page: number,
    pageSize: number = 4
  ): Observable<PaginatedResponse<Organisation>> {
    return this.http.get<PaginatedResponse<Organisation>>(
      `${this.apiUrl}/page?page=${page}&pageSize=${pageSize}`
    );
  }
  getOrganisations(): Observable<Organisation[]> {
    return this.http.get<Organisation[]>(this.apiUrl).pipe(
      map((organisations) =>
        organisations.map((org) => ({
          ...org,
          createdAt: org.createdAt ? new Date(org.createdAt) : undefined,
          updatedAt: org.updatedAt ? new Date(org.updatedAt) : undefined,
        }))
      )
    );
  }

  getOrganisation(id: number): Observable<Organisation> {
    return this.http.get<Organisation>(`${this.apiUrl}/${id}`).pipe(
      map((org) => ({
        ...org,
        createdAt: org.createdAt ? new Date(org.createdAt) : undefined,
        updatedAt: org.updatedAt ? new Date(org.updatedAt) : undefined,
      }))
    );
  }

  getOrganisationImage(id: number): Observable<Blob> {
    return this.http.get(`${this.apiUrl}/image/${id}`, {
      responseType: 'blob',
    });
  }

  createOrganisation(
    organisation: Partial<Organisation>,
    logo?: File
  ): Observable<Organisation> {
    const formData = new FormData();
    Object.keys(organisation).forEach((key) => {
      const value = organisation[key as keyof Partial<Organisation>];
      if (value !== null && value !== undefined) {
        formData.append(
          key,
          typeof value === 'object' ? JSON.stringify(value) : value.toString()
        );
      }
    });
    if (logo) formData.append('logo', logo);
    return this.http.post<Organisation>(this.apiUrl, formData);
  }

  updateOrganisation(
    id: number,
    organisation: Partial<Organisation>,
    logo?: File
  ): Observable<void> {
    const formData = new FormData();
    Object.keys(organisation).forEach((key) => {
      const value = organisation[key as keyof Partial<Organisation>];
      if (value !== null && value !== undefined) {
        formData.append(
          key,
          typeof value === 'object' ? JSON.stringify(value) : value.toString()
        );
      }
    });
    if (logo) formData.append('logo', logo);
    return this.http.put<void>(`${this.apiUrl}/${id}`, formData);
  }

  deleteOrganisation(id: number): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}/${id}`);
  }

  uploadOrganisationLogo(id: number, logo: File): Observable<any> {
    const formData = new FormData();
    formData.append('logo', logo);
    return this.http.post(`${this.apiUrl}/${id}/upload-logo`, formData);
  }

  getSitesByOrganisation(organisationId: number): Observable<Site[]> {
    return this.http.get<Site[]>(`${this.apiUrl}/${organisationId}/sites`);
  }

  // organisation.service.ts
  getOrganisationTypes(): OrganisationType[] {
    return [
      'Ecole',
      'Hopital',
      'Entrepot',
      'Bureau',
      'Usine',
      'Magasin',
      'Residence',
      'CentreCommercial',
      'Restaurant',
      'Hotel',
      'Maison',
    ];
  }

  // getOrganisationsByType(
  //   organisations: Organisation[]
  // ): { type: OrganisationType; count: number }[] {
  //   const typeCounts: Record<OrganisationType, number> = {} as Record<
  //     OrganisationType,
  //     number
  //   >;

  //   // Initialize all types with 0 count
  //   this.getOrganisationTypes().forEach((type) => {
  //     typeCounts[type] = 0;
  //   });

  //   // Count sites for each organization type
  //   organisations.forEach((org) => {
  //     typeCounts[org.type] += org.sites?.length ?? 0;
  //   });

  //   return Object.entries(typeCounts).map(([type, count]) => ({
  //     type: type as OrganisationType,
  //     count,
  //   }));
  // }

  getOrganisationsByType(
    organisations: Organisation[]
  ): { type: OrganisationType; count: number }[] {
    const counts: Record<OrganisationType, number> = {} as Record<
      OrganisationType,
      number
    >;

    // Initialize all types to 0
    this.getOrganisationTypes().forEach((type) => {
      counts[type] = 0;
    });

    // Count how many orgs are of each type
    organisations.forEach((org) => {
      counts[org.type] += 1;
    });

    return Object.entries(counts).map(([type, count]) => ({
      type: type as OrganisationType,
      count,
    }));
  }

  getValidSiteTypes(organisationId: number): Observable<string[]> {
    return this.http.get<string[]>(
      `${this.apiUrl}/${organisationId}/site-types`
    );
  }

  getImageUrl(imagePath: string): string {
    if (!imagePath) return 'assets/images/default-organisation.jpg';
    if (imagePath.startsWith('http')) return imagePath;
    return `${this.baseUrl}/images/${imagePath}`;
  }

  // organisation.service.ts
  getIconForOrganisationType(type: OrganisationType): string {
    const iconMap: Record<OrganisationType, string> = {
      Bureau: 'business',
      Entrepot: 'warehouse',
      Usine: 'factory',
      Magasin: 'store',
      Residence: 'home',
      CentreCommercial: 'shopping_cart',
      Restaurant: 'restaurant',
      Hotel: 'hotel',
      Ecole: 'school',
      Hopital: 'local_hospital',
      Maison: 'house',
    };
    return iconMap[type] || 'location_on';
  }

  getColorForOrganisationType(type: OrganisationType): string {
    const colorMap: Record<OrganisationType, string> = {
      Bureau: '#4CAF50',
      Entrepot: '#FF9800',
      Usine: '#F44336',
      Magasin: '#2196F3',
      Residence: '#9C27B0',
      CentreCommercial: '#FFEB3B',
      Restaurant: '#E91E63',
      Hotel: '#3F51B5',
      Ecole: '#009688',
      Hopital: '#607D8B',
      Maison: '#795548',
    };
    return colorMap[type] || '#4CAF50';
  }
}
