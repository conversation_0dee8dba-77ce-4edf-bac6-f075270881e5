import { Component, Input, Output, EventEmitter, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, Router } from '@angular/router';
import { trigger, state, style, transition, animate } from '@angular/animations';
import { Site } from '../../core/models/site';

@Component({
  selector: 'app-card-site',
  standalone: true,
  imports: [CommonModule, RouterModule],
  templateUrl: './card-site.component.html',
  styleUrls: ['./card-site.component.css'],
  animations: [
    trigger('cardHover', [
      state(
        'initial',
        style({
          transform: 'scale(1)',
          boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
        })
      ),
      state(
        'hovered',
        style({
          transform: 'scale(1.02)',
          boxShadow: '0 8px 20px rgba(0, 0, 0, 0.15)',
        })
      ),
      transition('initial <=> hovered', animate('0.2s ease-in-out')),
    ]),
  ],
})
export class CardSiteComponent implements OnInit {
  @Input() site!: Site;
  @Output() viewDetails = new EventEmitter<Site>();
  @Output() edit = new EventEmitter<Site>();
  @Output() delete = new EventEmitter<string>();
  @Output() addSite = new EventEmitter<void>();

  cardState = 'initial';
  currentImageIndex = 0;

  constructor(public router: Router) {}

  ngOnInit() {
    console.log('CardSite initialized for site:', {
      siteId: this.site.Id,
      hasImage: !!this.site.Images
    });
  }

  get imageUrl(): string {
    // Return the base64 image if it exists, otherwise return placeholder
    return this.site.Images ? `data:image/jpeg;base64,${this.site.Images}` : 'assets/images/placeholder.png';
  }

  onMouseEnter(): void {
    this.cardState = 'hovered';
  }

  onMouseLeave(): void {
    this.cardState = 'initial';
  }

  onView(event?: Event): void {
    if (event) event.stopPropagation();
    this.router.navigate(['/site-details', this.site.Id]);
  }

  onEdit(event?: Event): void {
    if (event) event.stopPropagation();
    this.edit.emit(this.site);
  }

  onDelete(event?: Event): void {
    if (event) event.stopPropagation();
    if (confirm('Êtes-vous sûr de vouloir supprimer ce site ?')) {
      this.delete.emit(this.site.Id);
    }
  }

  onAdd(): void {
    this.addSite.emit();
  }

  onImageError(event: any): void {
    console.error('Image load error');
    event.target.src = 'assets/images/placeholder.png';
  }
}