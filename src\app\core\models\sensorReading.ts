import { Capteur } from "./Capteur";

export interface SensorReading {
    idCapteur: string;
    capteur: Capteur | null;
    timestamp: string;
    battery: number | null;
    linkQuality: number | null;
    temperature: number | null;
    humidity: number | null;
    illuminance: number | null;
    power: number | null;
    energy: number | null;
    voltage: number | null;
    current: number | null;
    occupancy: boolean | null;
    tamper: boolean | null;
    contact: boolean | null;
    batteryLevel: number | null;
    value: number | null;
}

