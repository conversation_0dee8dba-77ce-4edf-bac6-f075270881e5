<div class="site-management-container">
  <div class="header-section">
    <div class="page-title">
      <h1 class="title">
        <mat-icon class="title-icon">location_on</mat-icon> Gestion des Sites
      </h1>
    </div>

    <div class="actions">
      <button
        class="create-button"
        (click)="showAddSiteForm()"
        *ngIf="!showCreateForm"
      >
        <mat-icon class="action-icon">add</mat-icon> Créer Site
      </button>
    </div>
  </div>

  <div class="create-form-card" *ngIf="showCreateForm" [@fadeIn]>
    <form [formGroup]="createSiteForm" (ngSubmit)="submitCreateForm()">
      <div class="form-grid">
        <div class="form-group">
          <label for="Nom">Nom</label>
          <input id="Nom" type="text" formControlName="Nom" required />
        </div>
        <div class="form-group">
          <label for="Pays">Pays</label>
          <input id="Pays" type="text" formControlName="Pays" required />
        </div>
        <div class="form-group">
          <label for="Ville">Ville</label>
          <input id="Ville" type="text" formControlName="Ville" required />
        </div>
        <div class="form-group">
          <label for="Rue">Rue</label>
          <input id="Rue" type="text" formControlName="Rue" />
        </div>
        <div class="form-group">
          <label for="TelephoneSurSite">Téléphone sur site</label>
          <input id="TelephoneSurSite" type="text" formControlName="TelephoneSurSite" />
        </div>
        <div class="form-group">
          <label for="Description">Description</label>
          <textarea id="Description" formControlName="Description"></textarea>
        </div>
        <div class="form-group">
          <label for="ContactDeSite">Contact de site</label>
          <input id="ContactDeSite" type="text" formControlName="ContactDeSite" />
        </div>
        <div class="form-group">
          <label for="IsEnabled">Actif</label>
          <input id="IsEnabled" type="checkbox" formControlName="IsEnabled" />
        </div>
        <div class="form-group">
  <label for="idClient">Organisation <span class="required">*</span></label>
  <select 
    id="id" 
    formControlName="IdClient" 
    class="form-select"
    required>
    <option value="">Sélectionnez une organisation</option>
    <option *ngFor="let org of clients" [value]="org.Id">
      {{ org.NomComplet }}
    </option>
  </select>
  <div class="error-message" *ngIf="createSiteForm.get('id')?.invalid && createSiteForm.get('id')?.touched">
    Veuillez sélectionner une organisation
  </div>
</div>
        <div class="form-group">
          <label for="Images">Images</label>
          <input type="file" id="Images" (change)="onImagesSelected($event)" multiple accept="image/*" />
        </div>
      </div>
      <div class="form-actions">
        <button type="button" (click)="hideAddSiteForm()">Annuler</button>
        <button type="submit" [disabled]="!createSiteForm.valid">Créer</button>
      </div>
    </form>
  </div>

  <div class="edit-form-card" *ngIf="showEditForm && selectedSite" [@fadeIn]>
    <form [formGroup]="editSiteForm" (ngSubmit)="submitEditForm()">
      <div class="form-grid">
    <div class="form-group">
      <label for="Nom">Nom</label>
      <input id="Nom" type="text" formControlName="Nom" required />
    </div>
    <div class="form-group">
      <label for="Pays">Pays</label>
      <input id="Pays" type="text" formControlName="Pays" required />
    </div>
    <div class="form-group">
      <label for="Ville">Ville</label>
      <input id="Ville" type="text" formControlName="Ville" required />
    </div>
    <div class="form-group">
      <label for="Rue">Rue</label>
      <input id="Rue" type="text" formControlName="Rue" />
    </div>
    <div class="form-group">
      <label for="TelephoneSurSite">Téléphone sur site</label>
      <input id="TelephoneSurSite" type="text" formControlName="TelephoneSurSite" />
    </div>
    <div class="form-group">
      <label for="Description">Description</label>
      <textarea id="Description" formControlName="Description"></textarea>
    </div>
    <div class="form-group">
      <label for="ContactDeSite">Contact de site</label>
      <input id="ContactDeSite" type="text" formControlName="ContactDeSite" />
    </div>
    <div class="form-group">
      <label for="IsEnabled">Actif</label>
      <input id="IsEnabled" type="checkbox" formControlName="IsEnabled" />
    </div>
    <div class="form-group">
      <label for="IdClient">ID Client</label>
      <input id="IdClient" type="text" formControlName="IdClient" required />
    </div>
    <div class="form-group">
      <label for="Images">Images</label>
      <input type="file" id="Images" (change)="onImagesSelected($event)" multiple accept="image/*" />
    </div>
  </div>

      <div class="form-actions">
        <button type="button" (click)="hideEditForm()">Annuler</button>
        <button type="submit" [disabled]="!editSiteForm.valid">Modifier</button>
      </div>
    </form>
  </div>

  <div class="search-bar">
    <input
      type="text"
      [(ngModel)]="searchTerm"
      (keyup)="filterSites()"
      placeholder="Rechercher un site"
    />
  </div>

  <div class="loading-spinner" *ngIf="isLoading">Chargement...</div>

  <div class="table-view" *ngIf="viewMode === 'table' && !isLoading">
    <app-generic-table
      [data]="filteredSites"
      [headers]="headers"
      [keys]="keys"
      [actions]="['edit', 'view', 'delete']"
      [pageSize]="pageSize"
      [currentPage]="currentPage"
      [totalCount]="totalCount"
      (pageChange)="onPageChange($event)"
      (actionTriggered)="handleAction($event)"
    ></app-generic-table>
  </div>
</div>
