import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';import { ApiService } from '../api.service';
import { RuleTransaction } from '@app/core/models/RuleTransaction';

@Injectable({ providedIn: 'root' })
export class RuleTransactionApiService extends ApiService<RuleTransaction> {
  constructor(public override http: HttpClient) {
    super(http);
    this.setEndpoint("rule-transaction");
  }
}

