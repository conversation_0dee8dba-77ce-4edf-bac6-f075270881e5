import { Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-notification',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="notification {{type}}" [class.with-icon]="icon">
      @if (icon) {
        <i class="material-icons icon">{{icon}}</i>
      }
      <div class="content">
        @if (title) {
          <h4 class="title">{{title}}</h4>
        }
        <p class="message">{{message}}</p>
      </div>
      <button class="close-btn" (click)="onClose.emit()">
        <i class="material-icons">close</i>
      </button>
    </div>
  `,
  styleUrls: ['./notification.component.css']
})
export class NotificationComponent {
  @Input() type: 'success' | 'error' | 'info' | 'warning' = 'info';
  @Input() title?: string;
  @Input() message: string = '';
  @Input() icon?: string;
  @Output() onClose = new EventEmitter<void>();
}