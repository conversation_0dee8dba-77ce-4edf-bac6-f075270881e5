import { Component, Input, Output, EventEmitter } from '@angular/core';
import { Subscription } from '../admin-subscription-dashboard/admin-subscription-dashboard.component';
import { CommonModule } from '@angular/common';
import { MatIconModule } from '@angular/material/icon';

@Component({
  selector: 'app-action-modal',
  templateUrl: './action-modal.component.html',
  styleUrls: ['./action-modal.component.css'],
  standalone: true,
  imports: [CommonModule, MatIconModule]
})
export class ActionModalComponent {
  @Input() subscription: Subscription | null = null;
  @Input() actionType: string = '';
  @Output() close = new EventEmitter<void>();
  @Output() confirm = new EventEmitter<void>();

  getActionLabel(): string {
    const labels: { [key: string]: string } = {
      activate: 'activer',
      pause: 'mettre en pause',
      deactivate: 'désactiver',
      ban: 'bannir'
    };
    return labels[this.actionType] || '';
  }

  getConfirmButtonColor(): string {
    return this.actionType === 'ban' ? 'bg-red-500' : 'bg-emerald-500';
  }
}
