.locals-container {
  max-width: 1300px;
  margin: 30px auto;
  padding: 25px;
  background: linear-gradient(145deg, #ffffff, #f9f9f9);
  border-radius: 12px;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.08);
}

/* Header section */
.header-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  animation: slideIn 0.5s ease-out;
}

.page-title {
  margin: 0;
}

.title {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 28px;
  font-weight: 700;
  color: #2d3748;
  margin-bottom: 8px;
  letter-spacing: 0.5px;
}

.title-icon {
  font-size: 30px;
  color: #4caf50;
  background: linear-gradient(45deg, #4caf50, #81c784);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.subtitle {
  font-size: 16px;
  color: #718096;
  margin: 0;
  font-style: italic;
}

.actions {
  display: flex;
  gap: 15px;
}

.create-button {
  background: linear-gradient(45deg, #4caf50, #81c784);
  color: white;
  padding: 10px 20px;
  border-radius: 8px;
  border: none;
  box-shadow: 0 3px 10px rgba(76, 175, 80, 0.3);
  transition: transform 0.3s ease, box-shadow 0.3s ease, background 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
}

.create-button:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 15px rgba(76, 175, 80, 0.5);
  background: linear-gradient(45deg, #81c784, #4caf50);
}

.view-toggle {
  border: 2px solid #4caf50;
  color: #4caf50;
  padding: 8px 16px;
  border-radius: 8px;
  background: transparent;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
}

.view-toggle:hover {
  background-color: #e8f5e9;
  transform: translateY(-2px);
}

.action-icon {
  font-size: 20px;
}

/* Create form card */
.create-form-card {
  margin-bottom: 30px;
  border-radius: 16px;
  box-shadow: 0 4px 18px rgba(76, 175, 80, 0.08);
  animation: fadeIn 0.4s ease-in 0.3s;
  background: white;
  padding: 2rem;
}

.create-form-card,
app-edit-local {
  scroll-margin-top: 20px; /* Adds some space when scrolling to the form */
}

/* Optional: Add a highlight animation when scrolled to */
@keyframes highlightForm {
  0% {
    box-shadow: 0 0 0 rgba(33, 150, 243, 0);
  }
  50% {
    box-shadow: 0 0 20px rgba(33, 150, 243, 0.3);
  }
  100% {
    box-shadow: 0 0 0 rgba(33, 150, 243, 0);
  }
}

.create-form-card:target,
app-edit-local:target {
  animation: highlightForm 1s ease-out;
}

.form-title {
  margin: 0 0 20px;
  font-size: 22px;
  font-weight: 600;
  color: #2e7d32;
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
  gap: 20px;
  margin-bottom: 25px;
}

.form-group {
  display: flex;
  flex-direction: column;
  margin-bottom: 15px;
}

.form-group label {
  font-size: 14px;
  color: #4a5568;
  margin-bottom: 6px;
  font-weight: 500;
}

.form-control {
  height: 40px;
  padding: 8px 12px;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.form-control:focus {
  border-color: #4caf50;
  box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.2);
  outline: none;
}

.form-error {
  font-size: 12px;
  color: #e53e3e;
  margin-top: 4px;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 15px;
}

.btn-cancel {
  padding: 10px 20px;
  background: transparent;
  border: 1px solid #cbd5e0;
  color: #718096;
  border-radius: 6px;
  transition: all 0.3s ease;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
}

.btn-cancel:hover {
  background-color: #f7fafc;
}

.btn-submit {
  padding: 10px 20px;
  background: linear-gradient(45deg, #4caf50, #81c784);
  color: white;
  border: none;
  border-radius: 6px;
  transition: all 0.3s ease;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  box-shadow: 0 2px 6px rgba(76, 175, 80, 0.3);
}

.btn-submit:hover {
  background: linear-gradient(45deg, #81c784, #4caf50);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(76, 175, 80, 0.4);
}

.btn-submit:disabled {
  background: #cbd5e0;
  transform: none;
  box-shadow: none;
  cursor: not-allowed;
}

/* Search bar */
.search-bar {
  margin-bottom: 30px;
}

.search-field {
  max-width: 450px;
  position: relative;
  border-radius: 8px;
  transition: all 0.3s ease;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  padding: 10px 15px;
  display: flex;
  align-items: center;
}

.search-field:focus-within {
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  border-color: #4caf50;
}

.search-icon {
  color: #718096;
  font-size: 20px;
  margin-right: 10px;
}

.search-field input {
  border: none;
  background: transparent;
  width: 100%;
  padding: 0;
  font-size: 15px;
  color: #4a5568;
}

.search-field input:focus {
  outline: none;
}

.clear-button {
  background: transparent;
  border: none;
  color: #a0aec0;
  padding: 0;
  cursor: pointer;
  transition: color 0.3s ease;
}

.clear-button:hover {
  color: #718096;
}

.clear-button .material-icons {
  font-size: 18px;
}

/* Loading container */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 250px;
  animation: fadeIn 0.5s ease-out;
}

.spinner {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: 3px solid rgba(76, 175, 80, 0.1);
  border-top-color: #4caf50;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.loading-container p {
  color: #718096;
  font-size: 16px;
  font-style: italic;
}

/* No data message */
.no-data {
  text-align: center;
  padding: 30px;
  color: #718096;
  animation: fadeIn 0.5s ease-out;
}

.no-data .material-icons {
  font-size: 60px;
  color: #4caf50;
  margin-bottom: 15px;
}
.back-button {
  background: #f5f5f5;
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  margin-right: 15px;
  margin-top: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
}
/* Cards container */
.cards-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 2rem;
  padding: 1rem 0;
}

/* Table container */
.table-container {
  overflow-x: auto;
  margin-bottom: 30px;
  border-radius: 16px;
  box-shadow: 0 4px 18px rgba(76, 175, 80, 0.08);
  background: #ffffff;
  overflow: hidden;
  margin-top: 2rem;
}

.data-table {
  width: 100%;
  border-collapse: collapse;
}

.data-table th {
  background: linear-gradient(45deg, #f5f5f5, #eceff1);
  font-weight: 600;
  color: #2d3748;
  padding: 1rem 1.5rem;
}

.data-table td {
  padding: 15px;
  border-bottom: 1px solid #edf2f7;
  color: #4a5568;
}

.available {
  color: #2e7d32;
  font-weight: 500;
}

.unavailable {
  color: #e53e3e;
  font-weight: 500;
}

.actions-cell {
  display: flex;
  gap: 8px;
}

.action-button {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  cursor: pointer;
  background: transparent;
}

.action-button .material-icons {
  font-size: 18px;
}

.action-button.view {
  color: #4caf50;
}

.action-button.view:hover {
  background-color: #e8f5e9;
  transform: scale(1.1);
}

.action-button.edit {
  color: #2196f3;
}

.action-button.edit:hover {
  background-color: #e3f2fd;
  transform: scale(1.1);
}

.action-button.delete {
  color: #ef5350;
}

.action-button.delete:hover {
  background-color: #ffebee;
  transform: scale(1.1);
}

/* Stats section */
.stats-section {
  margin-bottom: 2.5rem;
  margin-top: 1.5rem;
  animation: fadeIn 0.7s;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
  gap: 2rem;
}

.stat-card {
  background: linear-gradient(120deg, #f8fafc 60%, #e8f5e9 100%);
  border-radius: 16px;
  box-shadow: 0 4px 18px rgba(76, 175, 80, 0.08);
  display: flex;
  align-items: center;
  padding: 1.5rem 1.2rem;
  gap: 1.2rem;
  transition: box-shadow 0.3s, transform 0.3s;
}

.stat-card:hover {
  box-shadow: 0 8px 32px rgba(76, 175, 80, 0.18);
  transform: translateY(-4px) scale(1.03);
}

.organisation-details-container {
  max-width: 1300px;
  margin: 30px auto;
  padding: 25px;
  background: linear-gradient(145deg, #ffffff, #f9f9f9);
  border-radius: 12px;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.08);
}

/* Breadcrumb navigation */
.breadcrumb-nav {
  display: flex;
  align-items: center;
  margin-bottom: 25px;
}


.back-button {
  background: #f5f5f5;
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.stat-icon {
  width: 54px;
  height: 54px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
}

.back-button .material-icons {
  color: #555;
  font-size: 24px;
}


.back-button:hover {
  background: #e0e0e0;
  transform: translateX(-3px);
}

.back-button .material-icons {
  color: #555;
  font-size: 24px;
}

.breadcrumb-text {
  font-size: 16px;
  color: #666;
}


.stat-icon.room {
  background: #e8f5e9;
  color: #388e3c;
}
.stat-icon.energy {
  background: #e3f2fd;
  color: #1976d2;
}
.stat-icon.capacity {
  background: #f3e5f5;
  color: #8e24aa;
}
.stat-icon.area {
  background: #fff3e0;
  color: #f57c00;
}

.stat-info {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.stat-value {
  font-size: 2rem;
  font-weight: 700;
  color: #2d3748;
  margin-bottom: 0.2rem;
}

.stat-label {
  font-size: 1rem;
  color: #718096;
  font-weight: 500;
  letter-spacing: 0.01em;
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive design */
@media (max-width: 768px) {
  .header-section {
    flex-direction: column;
    align-items: flex-start;
    gap: 20px;
  }

  .actions {
    width: 100%;
    flex-direction: column;
    gap: 10px;
  }

  .create-button,
  .view-toggle {
    width: 100%;
    padding: 12px;
    justify-content: center;
  }

  .cards-container {
    grid-template-columns: 1fr;
    flex-grow: 1;
  }

  .form-grid {
    grid-template-columns: 1fr;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
  }
}

@media (max-width: 480px) {
  .title {
    font-size: 24px;
  }

  .title-icon {
    font-size: 26px;
  }

  .subtitle {
    font-size: 14px;
  }

  .search-field {
    max-width: 100%;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .stat-card {
    padding: 1rem;
  }

  .stat-value {
    font-size: 1.5rem;
  }
}

/* Add these styles to both component CSS files */

.file-upload {
  position: relative;
  margin-bottom: 1rem;
}

.file-upload input[type="file"] {
  padding: 8px;
  border: 2px dashed #e0e0e0;
  border-radius: 8px;
  width: 100%;
  cursor: pointer;
}

.preview {
  margin-top: 8px;
  max-width: 200px;
  border-radius: 4px;
  overflow: hidden;
}

.preview img {
  width: 100%;
  height: auto;
  object-fit: cover;
}

.tags-input {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.tags-input input {
  padding: 8px 12px;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  font-size: 14px;
}

.tags-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.tag {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  background-color: #e3f2fd;
  color: #1976d2;
  border-radius: 16px;
  font-size: 14px;
}

.remove-tag {
  cursor: pointer;
  font-size: 18px;
  color: #1976d2;
}

.remove-tag:hover {
  color: #d32f2f;
}

/* Add these styles */
.pagination-controls {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 0.5rem;
  padding: 1.5rem;
  margin-top: 2rem;
  border-top: 1px solid #e5e7eb;
}

.page-numbers {
  display: flex;
  gap: 0.25rem;
  align-items: center;
}

.page-number-button {
  min-width: 32px;
  height: 32px;
  border-radius: 4px;
  border: 1px solid #e2e8f0;
  background: white;
  color: #4a5568;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.875rem;
}

.page-number-button:hover:not(:disabled):not(.active) {
  background: #f7fafc;
  border-color: #cbd5e0;
}

.page-number-button.active {
  background: #4caf50;
  color: white;
  border-color: #4caf50;
}

.page-number-button.separator {
  border: none;
  background: transparent;
  cursor: default;
}

.page-number-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pagination-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 4px;
  border: 1px solid #e2e8f0;
  background: white;
  color: #4a5568;
  cursor: pointer;
  transition: all 0.2s ease;
}

.pagination-button:hover:not(:disabled) {
  background: #f7fafc;
  border-color: #cbd5e0;
}

.pagination-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Update cards container for pagination */
.cards-view-container {
  display: flex;
  flex-direction: column;
  min-height: calc(100vh - 400px);
}
