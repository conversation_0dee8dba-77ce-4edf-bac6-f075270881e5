.container {
  max-width: 1200px;
  margin: 20px auto;
  padding: 20px;
  background: var(--container-bg);
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  animation: cardEntrance 0.5s ease-out;
}

.page-title {
  font-size: 28px;
  font-weight: 500;
  color: var(--green-main);
  display: flex;
  align-items: center;
  gap: 10px;
}


.sensor-dashboard {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
  padding: 20px;
}

.sensor-card {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  padding: 15px;
  font-family: Arial, sans-serif;
}

.sensor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  border-bottom: 1px solid #eee;
  padding-bottom: 10px;
}

.sensor-title {
  display: flex;
  align-items: center;
  gap: 8px;
}

.sensor-icon {
  color: #4285f4;
  font-size: 24px;
  width: 24px;
  height: 24px;
}

.sensor-header h3 {
  margin: 0;
  font-size: 16px;
  color: #333;
}

.signal-quality {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #666;
  background: #f5f5f5;
  padding: 3px 8px;
  border-radius: 12px;
}

.signal-quality mat-icon {
  font-size: 16px;
  width: 16px;
  height: 16px;
  color: #4caf50;
}

.sensor-data {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.data-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.data-label {
  display: flex;
  align-items: center;
  gap: 6px;
  font-weight: bold;
  color: #555;
}

.data-label mat-icon {
  font-size: 18px;
  width: 18px;
  height: 18px;
  color: #666;
}

.data-value {
  color: #333;
}

@keyframes warningPulse {
  0% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.05);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

.warning {
  color: #e74c3c;
  font-weight: bold;
  animation: warningPulse 1.5s ease-in-out infinite;
  display: inline-block; /* Ensures transform works properly */
  padding: 2px 6px; /* Add some padding for better visual effect */
  border-radius: 4px; /* Optional: rounds the corners */
  position: relative; /* Required for pseudo-elements if needed */
}

/* Optional: Add a subtle glow effect */
.warning::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(231, 76, 60, 0.1);
  border-radius: 4px;
  z-index: -1;
  animation: warningGlow 1.5s ease-in-out infinite;
}

@keyframes warningGlow {
  0% {
    opacity: 0.5;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.5;
  }
}


/* Animation */
@keyframes cardEntrance {
  from {
    opacity: 0;
    transform: scale(0.95) translateY(20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.sensor-status {
  display: flex;
  gap: 8px;
  align-items: center;
}

.battery-level {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #666;
  background: #f5f5f5;
  padding: 3px 8px;
  border-radius: 12px;
}

.battery-level mat-icon {
  font-size: 16px;
  width: 16px;
  height: 16px;
  color: #4caf50;
}

.battery-low {
  color: #e74c3c;
}

.battery-low mat-icon {
  color: #e74c3c;
  animation: warningPulse 1.3s ease-in-out infinite;
}