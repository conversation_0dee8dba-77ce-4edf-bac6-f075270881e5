import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router } from '@angular/router';
import { FormsModule, ReactiveFormsModule, FormGroup, FormControl, Validators } from '@angular/forms';
import { LocalService } from '../../../core/services/local.service';
import { trigger, state, style, transition, animate } from '@angular/animations';
import { Local } from '../../../core/models/local.model';
import { PageEvent } from '@angular/material/paginator'; // Import PageEvent
import { GenericTableComponent } from '@app/components/generic-table/generic-table.component';

interface LocalFormValues {
  type: string | null;
  etage: number | null;
  surface: number | null;
  hauteurSousPlafond: number | null;
  capacitePersonnes: number | null;
  description: string | null;
  temperatureCible: number | null;
  consommationElectriqueMensuelle: number | null;
  dateDerniereMaintenance: string | null;
  siteId: number | null;
}


@Component({
  selector: 'app-local-management',
  standalone: true,
  imports: [CommonModule, FormsModule, ReactiveFormsModule, GenericTableComponent], // Add GenericTableComponent here
  templateUrl: './local-management.component.html',
  styleUrls: ['./local-management.component.css'],
  animations: [
    trigger('tableRowAnimation', [
      state('void', style({ opacity: 0, transform: 'translateY(20px)' })),
      transition('void => *', animate('300ms ease-in'))
    ]),
    trigger('fadeIn', [
      state('void', style({ opacity: 0 })),
      transition('void => *', animate('400ms 300ms ease-in'))
    ])
  ]
})
export class LocalManagementComponent implements OnInit {
  locals: Local[] = [];
  filteredLocals: Local[] = []; // This will be the data passed to the generic table
  searchTerm: string = '';
  viewMode: 'cards' | 'table' = 'cards';
  isLoading: boolean = true;
  showCreateForm: boolean = false;
  siteId?: number;
  localImages: { [key: number]: string } = {};
  architecture2DFile?: File;
  localeImageFile?: File;

  currentPage: number = 1; // 1-based index for your component's logic
  pageSize: number = 5;
  totalPages: number = 1;
  totalCount: number = 0; // Total count of all locals
  totalFilteredLocalsCount: number = 0; // Total count of filtered locals

  localTypes = [
    'Bureau',
    'SalleReunion',
    'OpenSpace',
    'Cafeteria',
    'SalleServeur',
    'Archive',
    'Stockage',
    'SanitaireHomme',
    'SanitaireFemme',
    'Reception'
  ];

createLocalForm = new FormGroup({
  type: new FormControl('Bureau', [Validators.required]),
  etage: new FormControl<number>(0),
  surface: new FormControl<number>(0),
  hauteurSousPlafond: new FormControl<number>(0),
  capacitePersonnes: new FormControl<number>(0),
  description: new FormControl(''),
  temperatureCible: new FormControl<number>(20),
  consommationElectriqueMensuelle: new FormControl<number>(0),
  dateDerniereMaintenance: new FormControl(''),
  siteId: new FormControl<number>(0, [Validators.required])
});

  showEditForm: boolean = false;
  selectedLocal: Local | null = null;
editLocalForm = new FormGroup({
  type: new FormControl('Bureau', [Validators.required]),
  etage: new FormControl<number>(0),
  surface: new FormControl<number>(0),
  hauteurSousPlafond: new FormControl<number>(0),
  capacitePersonnes: new FormControl<number>(0),
  description: new FormControl(''),
  temperatureCible: new FormControl<number>(20),
  consommationElectriqueMensuelle: new FormControl<number>(0),
  dateDerniereMaintenance: new FormControl(''),
  siteId: new FormControl<number>(0, [Validators.required])
});

  constructor(
    private readonly localService: LocalService,
    private readonly router: Router,
    private readonly route: ActivatedRoute
  ) {}

  ngOnInit(): void {
    this.loadAllLocals();

    this.route.queryParams.subscribe(params => {
      if (params['action'] === 'create') {
        this.showAddLocalForm();
      }
    });
  }

loadAllLocals(): void {
    this.isLoading = true;
    this.localService.getLocals().subscribe({
      next: (locals) => {
        this.locals = locals;
        this.totalCount = locals.length; // Set total count for all locals
        this.filterLocals(); // Apply initial filtering and pagination

        // Load images for each local
        this.locals.forEach(local => {
          if (local.imageArchitecture2D) {
            this.loadLocalImage(Number(local.imageArchitecture2D), 'architecture');
          }
          if (local.imageLocale) {
            this.loadLocalImage(Number(local.imageLocale), 'locale');
          }
        });

        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading locals:', error);
        
        this.isLoading = false;
      }
    });
  }

  filterLocals(): void {
    let tempFilteredLocals = [...this.locals];

    if (this.searchTerm) {
      const term = this.searchTerm.toLowerCase();
      tempFilteredLocals = this.locals.filter(local =>
        local.type?.toLowerCase().includes(term) ||
        local.description?.toLowerCase().includes(term) // Added description to search
      );
    }

    this.totalFilteredLocalsCount = tempFilteredLocals.length;
    this.totalPages = Math.ceil(this.totalFilteredLocalsCount / this.pageSize);

    // Ensure currentPage is valid after filtering
    if (this.currentPage > this.totalPages && this.totalPages > 0) {
      this.currentPage = this.totalPages;
    } else if (this.totalPages === 0) {
      this.currentPage = 1; // Or set to 0 depending on desired behavior for empty results
    }

    const startIndex = (this.currentPage - 1) * this.pageSize;
    const endIndex = startIndex + this.pageSize;
    this.filteredLocals = tempFilteredLocals.slice(startIndex, endIndex);
  }


  private loadLocalImage(imageId: number, type: 'architecture' | 'locale'): void {
    if (!this.localImages[imageId]) {
      const imageObservable = type === 'architecture'
        ? this.localService.getLocalImageArchitecture(imageId)
        : this.localService.getLocalImageLocale(imageId);

      imageObservable.subscribe({
        next: (blob) => {
          const reader = new FileReader();
          reader.onloadend = () => {
            this.localImages[imageId] = reader.result as string;
          };
          reader.readAsDataURL(blob);
        },
        error: (error) => console.error(`Error loading ${type} image ${imageId}:`, error)
      });
    }
  }

  toggleViewMode(): void {
    this.viewMode = this.viewMode === 'cards' ? 'table' : 'cards';
  }

  showAddLocalForm(): void {
    this.showCreateForm = true;
    if (this.siteId) {
      this.createLocalForm.patchValue({ siteId: this.siteId });
    }
  }

  hideAddLocalForm(): void {
    this.showCreateForm = false;
    this.createLocalForm.reset();
    this.architecture2DFile = undefined;
    this.localeImageFile = undefined;
  }

  onArchitecture2DSelected(event: any): void {
    if (event.target.files && event.target.files.length > 0) {
      this.architecture2DFile = event.target.files[0];
    }
  }

  onLocaleImageSelected(event: any): void {
    if (event.target.files && event.target.files.length > 0) {
      this.localeImageFile = event.target.files[0];
    }
  }

  submitCreateForm(): void {
    if (this.createLocalForm.valid) {
      const formData = new FormData();
      const formValues = this.createLocalForm.value as LocalFormValues;

      // Append form values
      Object.entries(formValues).forEach(([key, value]) => {
        if (value !== null && value !== undefined) {
          formData.append(key, value.toString());
        }
      });

      // Append images
      if (this.architecture2DFile) {
        formData.append('ImageArchitecture2D', this.architecture2DFile);
      }
      if (this.localeImageFile) {
        formData.append('ImageLocale', this.localeImageFile);
      }

      this.localService.createLocal(formData).subscribe({
        next: (newLocal) => {
          this.loadAllLocals();
          this.hideAddLocalForm();
          // this.notificationService.show({
          //   type: 'success',
          //   title: 'Succès',
          //   message: 'Local créé avec succès'
          // });
        },
        error: (error) => {
          console.error('Error creating local:', error);
          // this.notificationService.show({
          //   type: 'error',
          //   title: 'Erreur',
          //   message: 'Erreur lors de la création'
          // });
        }
      });
    }
  }

  editLocal(id: number): void {
    this.selectedLocal = this.locals.find(local => local.id === id) || null;
    if (this.selectedLocal) {
      this.editLocalForm.patchValue({
        type: this.selectedLocal.type,
        etage: this.selectedLocal.etage,
        surface: this.selectedLocal.surface,
        hauteurSousPlafond: this.selectedLocal.hauteurSousPlafond,
        capacitePersonnes: this.selectedLocal.capacitePersonnes,
        description: this.selectedLocal.description,
        temperatureCible: this.selectedLocal.temperatureCible,
        consommationElectriqueMensuelle: this.selectedLocal.consommationElectriqueMensuelle,
        dateDerniereMaintenance: this.selectedLocal.dateDerniereMaintenance,
        siteId: this.selectedLocal.siteId
      });
      this.showEditForm = true;
    }
  }

  hideEditForm(): void {
    this.showEditForm = false;
    this.selectedLocal = null;
    this.editLocalForm.reset();
  }

  submitEditForm(): void {
    if (this.editLocalForm.valid && this.selectedLocal) {
      const formData = new FormData();
      const formValues = this.editLocalForm.value as LocalFormValues;

      // Add the ID to the form data
      formData.append('id', this.selectedLocal.id.toString());

      // Append form values
      Object.entries(formValues).forEach(([key, value]) => {
        if (value !== null && value !== undefined) {
          formData.append(key, value.toString());
        }
      });

      // Add required SiteId if not included in formValues
      if (!formValues.siteId && this.selectedLocal.siteId) {
        formData.append('siteId', this.selectedLocal.siteId.toString());
      }

      // Append images if changed
      if (this.architecture2DFile) {
        formData.append('imageArchitecture2D', this.architecture2DFile);
      }
      if (this.localeImageFile) {
        formData.append('imageLocale', this.localeImageFile);
      }

      this.localService.updateLocal(this.selectedLocal.id, formData).subscribe({
        next: () => {
          this.loadAllLocals();
          this.hideEditForm();
          // this.notificationService.show({
          //   type: 'success',
          //   title: 'Succès',
          //   message: 'Local modifié avec succès'
          // });
        },
        error: (error) => {
          console.error('Error updating local:', error);
          // this.notificationService.show({
          //   type: 'error',
          //   title: 'Erreur',
          //   message: 'Erreur lors de la modification'
          // });
        }
      });
    }
  }

  deleteLocal(id: number): void {
    if (confirm('Êtes-vous sûr de vouloir supprimer ce local ?')) {
      this.localService.deleteLocal(id).subscribe({
        next: () => {
          this.loadAllLocals();
          // this.notificationService.show({
          //   type: 'success',
          //   title: 'Succès',
          //   message: 'Local supprimé avec succès'
          // });
        },
        error: (error) => {
          console.error('Error deleting local:', error);
          // this.notificationService.show({
          //   type: 'error',
          //   title: 'Erreur',
          //   message: 'Erreur lors de la suppression'
          // });
        }
      });
    }
  }

  onPageChange(event: PageEvent): void {
    this.currentPage = event.pageIndex + 1; // Convert 0-based to 1-based
    this.pageSize = event.pageSize;
    this.filterLocals(); // Re-apply filter and pagination for the new page
  }

  handleTableAction(event: { action: string, row: any }): void {
    const localId = event.row.id;
    switch (event.action) {
      case 'view':
        this.viewDetails(localId);
        break;
      case 'edit':
        this.editLocal(localId);
        break;
      case 'delete':
        this.deleteLocal(localId);
        break;
    }
  }

  viewDetails(id: number): void {
    this.router.navigate(['/site-details/', id]);
  }
}