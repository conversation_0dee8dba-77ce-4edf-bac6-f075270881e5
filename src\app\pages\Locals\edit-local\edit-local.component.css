.edit-form-container {
  max-width: 1000px;
  margin: 30px auto;
  padding: 25px;
  background: linear-gradient(145deg, #ffffff, #f9f9f9);
  border-radius: 12px;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.08);
  animation: fadeIn 0.5s ease-out;
}

.edit-form-card {
  background: white;
  border-radius: 12px;
  padding: 30px;
  position: relative;
}

.form-title {
  color: #1976D2;
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 30px;
  padding-bottom: 15px;
  border-bottom: 2px solid #e3f2fd;
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 24px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-group.full-width {
  grid-column: 1 / -1;
}

.form-group label {
  color: #455a64;
  font-size: 14px;
  font-weight: 500;
  letter-spacing: 0.3px;
}

select, input, textarea {
  padding: 12px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  font-size: 14px;
  color: #37474f;
  background-color: #fff;
  transition: all 0.3s ease;
}

select {
  appearance: none;
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23455a64' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 12px center;
  background-size: 16px;
  padding-right: 40px;
}

input[type="number"] {
  -moz-appearance: textfield;
}

input[type="number"]::-webkit-outer-spin-button,
input[type="number"]::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

textarea {
  min-height: 120px;
  resize: vertical;
}

select:focus, input:focus, textarea:focus {
  outline: none;
  border-color: #1976D2;
  box-shadow: 0 0 0 3px rgba(25, 118, 210, 0.1);
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 16px;
  margin-top: 32px;
  padding-top: 24px;
  border-top: 1px solid #e3f2fd;
}

.btn {
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  transition: all 0.3s ease;
  cursor: pointer;
}

.btn-secondary {
  background-color: #f5f5f5;
  color: #455a64;
  border: 1px solid #e0e0e0;
}

.btn-secondary:hover {
  background-color: #eeeeee;
  border-color: #bdbdbd;
}

.btn-primary {
  background: linear-gradient(135deg, #1976D2, #2196F3);
  color: white;
  border: none;
  box-shadow: 0 4px 12px rgba(33, 150, 243, 0.3);
}

.btn-primary:hover:not(:disabled) {
  background: linear-gradient(135deg, #1565C0, #1976D2);
  transform: translateY(-1px);
  box-shadow: 0 6px 16px rgba(33, 150, 243, 0.4);
}

.btn-primary:disabled {
  background: #bdbdbd;
  cursor: not-allowed;
  box-shadow: none;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@media (max-width: 768px) {
  .edit-form-container {
    margin: 15px;
    padding: 20px;
  }

  .edit-form-card {
    padding: 20px;
  }

  .form-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .form-actions {
    flex-direction: column-reverse;
    gap: 12px;
  }

  .btn {
    width: 100%;
    padding: 14px;
  }
}


/* Add these styles to both component CSS files */

.file-upload {
  position: relative;
  margin-bottom: 1rem;
}

.file-upload input[type="file"] {
  padding: 8px;
  border: 2px dashed #e0e0e0;
  border-radius: 8px;
  width: 100%;
  cursor: pointer;
}

.preview {
  margin-top: 8px;
  max-width: 200px;
  border-radius: 4px;
  overflow: hidden;
}

.preview img {
  width: 100%;
  height: auto;
  object-fit: cover;
}

.tags-input {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.tags-input input {
  padding: 8px 12px;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  font-size: 14px;
}

.tags-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.tag {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  background-color: #e3f2fd;
  color: #1976d2;
  border-radius: 16px;
  font-size: 14px;
}

.remove-tag {
  cursor: pointer;
  font-size: 18px;
  color: #1976d2;
}

.remove-tag:hover {
  color: #d32f2f;
}