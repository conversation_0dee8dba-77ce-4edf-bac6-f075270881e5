import { ControllerServerController } from "./controllerServerController";
import { ControllerServerRule } from "./controllerServerRule";
import { Licence } from "./licence";



export interface ControllerServeur {
    nom: string;
    ipAdresse: string;
    idLicence: string;
    licence: Licence;
    controllerServerControllers: ControllerServerController[];
    controllerServerRules: ControllerServerRule[];
}


