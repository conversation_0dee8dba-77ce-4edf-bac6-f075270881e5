<div class="subscription-table-container">
  <div class="table-color-bar"></div>
  <div class="table-wrapper">
    <table class="generic-table">
      <thead>
        <tr>
          <th *ngFor="let header of headers">{{ header }}</th>
          <th *ngIf="actions.length > 0">Actions</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let row of pagedData" class="table-row">
          <td *ngFor="let key of keys">{{ row[key] }}</td>
          <td *ngIf="actions.length > 0">
            <button mat-icon-button [matMenuTriggerFor]="actionsMenu" class="more-actions-button">
              <mat-icon>more_vert</mat-icon>
            </button>

            <mat-menu #actionsMenu="matMenu">
              <button
                mat-menu-item
                *ngFor="let action of actions"
                (click)="triggerAction(action, row)"
                class="menu-action-item"
              >
                <mat-icon [ngClass]="getActionClass(action)">{{ getActionIcon(action) }}</mat-icon>
                <span>{{ getAction<PERSON>abel(action) }}</span> </button>
            </mat-menu>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
  <mat-paginator
    [length]="totalCount"
    [pageSize]="pageSize"
    [pageIndex]="currentPage"
    [pageSizeOptions]="[5, 10, 25, 50]"
    (page)="onPageChange($event)"
    aria-label="Select page"
  >
  </mat-paginator>
</div>