import { SensorReading } from "./sensorReading";
import { Transaction } from "./transaction";
import { TypeCapteur } from "./typeCapteur";

export interface Capteur  {
    protocol: string;
    manufacturer: string;
    modelIdentifier: string;
    friendlyName: string;
    lastSeen: string;
    ieeeAddress: string;
    networkAddress: number;
    endpoint: number;
    nodeId: number;
    homeId: number;
    securityClasses: string;
    sensorReadings: SensorReading[];
    transactions: Transaction[];
    idTypeCapteur: string;
    typeCapteur: TypeCapteur | null;
}