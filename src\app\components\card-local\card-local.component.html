<div class="local-card">
  <div class="card-header">
    <!-- <h3>{{local.type}}</h3> -->
    <!-- <span class="local-id">#{{local.id}}</span> -->
  </div>
         <div class="image-section">
  <img *ngIf="imageLocaleUrl" [src]="imageLocaleUrl" alt="local">
</div>
  
  <div class="card-content">
    <div class="info-row">
      <span class="info-label">Étage:</span>
      <span class="info-value">{{local.etage}}</span>
    </div>
    <div class="info-row">
      <span class="info-label">Surface:</span>
      <span class="info-value">{{local.surface}} m²</span>
    </div>
    <div class="info-row">
      <span class="info-label">Capacité:</span>
      <span class="info-value">{{local.capacitePersonnes}} personnes</span>
    </div>
    <div class="info-row">
      <span class="info-label">Température:</span>
      <!-- <span class="info-value">{{local.temperatureCible}}°C</span> -->
    </div>
    <div class="info-row">
      <span class="info-label">Consommation:</span>
      <!-- <span class="info-value">{{local.consommationElectriqueMensuelle}} kWh</span> -->
    </div>
  </div>

  <div class="tags-container">
    <!-- <span class="tag" *ngFor="let tag of local.tags">{{tag.tag}}</span> -->
  </div>

  <div class="card-actions">
    <button class="action-btn" (click)="onView()">
      <mat-icon>visibility</mat-icon>
    </button>
    <button class="action-btn" (click)="onEdit()">
      <mat-icon>edit</mat-icon>
    </button>
    <button class="action-btn delete" (click)="onDelete()">
      <mat-icon>delete</mat-icon>
    </button>
  </div>
</div>