<div class="organisation-management-container">
  <div class="header-section">
    <div class="page-title">
      <h1 class="title">
        <i class="material-icons title-icon">people</i> Gestion des Clients
      </h1>
    </div>

    <!-- <div class="search-section">
      <div class="search-container">
        <i class="material-icons search-icon">search</i>
        <input 
          type="text" 
          placeholder="Rechercher un client..." 
          [(ngModel)]="searchTerm" 
          (input)="onSearchChange()"
          class="search-input">
      </div>
    </div> -->

    <div class="actions">
      <button class="create-button" (click)="showAddClientForm()">
        <i class="material-icons action-icon">add</i> Créer Client
      </button>
    </div>
  </div>

  <!-- Loading indicator -->
  <div *ngIf="isLoading" class="loading-container">
    <div class="loading-spinner"></div>
    <p>Chargement des clients...</p>
  </div>

  <!-- Create form -->
  <div class="create-form-card" *ngIf="showCreateForm" [@fadeIn]>
    <div class="form-header">
      <h2>Créer un nouveau client</h2>
    </div>
    
    <form [formGroup]="createClientForm" (ngSubmit)="submitCreateForm()">
      <div class="form-grid">
        <div class="form-group">
          <label for="nomComplet">Nom Complet <span class="required">*</span></label>
          <input 
            id="nomComplet" 
            type="text" 
            formControlName="nomComplet" 
            placeholder="Entrez le nom complet"
            required>
          <div class="error-message" *ngIf="createClientForm.get('nomComplet')?.invalid && createClientForm.get('nomComplet')?.touched">
            Le nom complet est obligatoire
          </div>
        </div>

        <div class="form-group">
          <label for="email">Email <span class="required">*</span></label>
          <input 
            id="email" 
            type="email" 
            formControlName="email" 
            placeholder="<EMAIL>"
            required>
          <div class="error-message" *ngIf="createClientForm.get('email')?.invalid && createClientForm.get('email')?.touched">
            Un email valide est obligatoire
          </div>
        </div>

        <div class="form-group">
          <label for="telephone">Téléphone <span class="required">*</span></label>
          <input 
            id="telephone" 
            type="tel" 
            formControlName="telephone" 
            placeholder="+33 1 23 45 67 89"
            required>
          <div class="error-message" *ngIf="createClientForm.get('telephone')?.invalid && createClientForm.get('telephone')?.touched">
            Le téléphone est obligatoire
          </div>
        </div>

        <div class="form-group">
          <label for="idOrganisation">Organisation <span class="required">*</span></label>
          <select 
            id="idOrganisation" 
            formControlName="idOrganisation" 
            class="form-select"
            required>
            <option value="">Sélectionnez une organisation</option>
            <option *ngFor="let org of organisations" [value]="org.Id">
              {{ org.Nom }}
            </option>
          </select>
          <div class="error-message" *ngIf="createClientForm.get('idOrganisation')?.invalid && createClientForm.get('idOrganisation')?.touched">
            Veuillez sélectionner une organisation
          </div>
        </div>

        <div class="form-group">
          <label for="password">Mot de passe <span class="required">*</span></label>
          <input 
            id="password" 
            type="password" 
            formControlName="password" 
            placeholder="Minimum 6 caractères"
            required>
          <div class="error-message" *ngIf="createClientForm.get('password')?.invalid && createClientForm.get('password')?.touched">
            Le mot de passe doit contenir au moins 6 caractères
          </div>
        </div>

        <div class="form-group">
          <label for="passwordConfirmed">Confirmer mot de passe <span class="required">*</span></label>
          <input 
            id="passwordConfirmed" 
            type="password" 
            formControlName="passwordConfirmed" 
            placeholder="Répétez le mot de passe"
            required>
          <div class="error-message" *ngIf="createClientForm.get('passwordConfirmed')?.invalid && createClientForm.get('passwordConfirmed')?.touched">
            La confirmation du mot de passe est obligatoire
          </div>
        </div>

        <div class="form-group checkbox-group">
          <label class="checkbox-label">
            <input 
              type="checkbox" 
              formControlName="isEnabled">
            <span class="checkmark"></span>
            Client actif
          </label>
        </div>
      </div>

      <div class="form-actions">
        <button type="button" class="cancel-button" (click)="hideAddClientForm()">
          <i class="material-icons">cancel</i> Annuler
        </button>
        <button type="submit" class="submit-button" [disabled]="!createClientForm.valid">
          <i class="material-icons">save</i> Créer Client
        </button>
      </div>
    </form>
  </div>

  <!-- Clients table -->
  <div class="table-section" *ngIf="!isLoading">
    <div class="table-header">
      <h3>Liste des Clients ({{ filteredClients.length }})</h3>
    </div>
    
    <app-generic-table
      [data]="filteredClients"
      [headers]="headers"
      [keys]="keys"
      [actions]="['edit', 'view', 'delete']"
      (actionTriggered)="handleAction($event)"
    ></app-generic-table>

  </div>
</div>