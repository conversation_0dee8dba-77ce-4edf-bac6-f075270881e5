  /* Base Styles */
  .min-h-screen {
    min-height: 100vh;
    background: var(--background-dark);
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    color: var(--text-light);
    padding: 2rem;
  }
  
  /* Container */
  .max-w-7xl {
    max-width: 80rem;
    margin: 0 auto;
  }
  
  /* Header */
  .text-center {
    text-align: center;
  }
  
  .text-4xl {
    font-size: 2.5rem;
    font-weight: 800;
    letter-spacing: -0.025em;
    margin-bottom: 0.5rem;
  }
  
  .text-2xl {
    font-size: 1.75rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 0.75rem;
  }
  
  .text-slate-300 {
    color: var(--text-muted);
  }
  
  /* Grid Layout */
  .grid {
    display: grid;
    gap: 2rem;
  }
  
  .xl\:grid-cols-3 {
    grid-template-columns: 2fr 1fr;
  }
  
  /* Card Styles */
  .bg-white\/10 {
    background-color: rgba(255, 255, 255, 0.1);
  }
  
  .backdrop-blur-lg {
    backdrop-filter: blur(10px);
  }
  
  .rounded-2xl {
    border-radius: 1rem;
  }
  
  .p-6 {
    padding: 1.5rem;
  }
  
  .border {
    border: 1px solid var(--border-color);
  }
  
  .border-white\/20 {
    border-color: rgba(255, 255, 255, 0.2);
  }
  
  .text-xl {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1rem;
  }
  
  /* Room Architecture */
  .relative {
    position: relative;
  }
  
  .w-full {
    width: 100%;
  }
  
  .h-96 {
    height: 24rem;
  }
  
  .bg-gradient-to-br {
    background: linear-gradient(to bottom right, #e5e7eb, #d1d5db);
  }
  
  .rounded-lg {
    border-radius: 0.5rem;
  }
  
  .overflow-hidden {
    overflow: hidden;
  }
  
  .absolute {
    position: absolute;
  }
  
  .inset-4 {
    top: 1rem;
    right: 1rem;
    bottom: 1rem;
    left: 1rem;
  }
  
  .inset-0 {
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
  }
  
  .bg-gradient-to-br.from-gray-100 {
    background: linear-gradient(to bottom right, var(--background-light), #d1d5db);
  }
  
  .shadow-inner {
    box-shadow: inset 0 2px 8px rgba(0, 0, 0, 0.15);
  }
  
  .transform {
    transform: perspective(1000px) rotateX(8deg);
  }
  
  .opacity-15 {
    opacity: 0.15;
  }
  
  /* Windows */
  .top-0 {
    top: 0;
  }
  
  .left-1\/4 {
    left: 25%;
  }
  
  .left-1\/2 {
    left: 50%;
  }
  
  .right-1\/4 {
    right: 25%;
  }
  
  .w-16 {
    width: 4rem;
  }
  
  .h-2 {
    height: 0.5rem;
  }
  
  .bg-blue-200 {
    background-color: #bfdbfe;
  }
  
  .border-gray-400 {
    border-color: #9ca3af;
  }
  
  .rounded-sm {
    border-radius: 0.125rem;
  }
  
  .shadow-md {
    box-shadow: var(--shadow-md);
  }
  
  .-translate-x-1\/2 {
    transform: translateX(-50%);
  }
  
  /* Tables */
  .top-1\/4 {
    top: 25%;
  }
  
  .top-2\/3 {
    top: 66%;
  }
  
  .left-1\/5 {
    left: 20%;
  }
  
  .right-1\/5 {
    right: 20%;
  }
  
  .w-20 {
    width: 5rem;
  }
  
  .h-10 {
    height: 2.5rem;
  }
  
  .bg-amber-700 {
    background-color: #92400e;
  }
  
  .border-amber-800 {
    border-color: #78350f;
  }
  
  /* Chairs */
  .left-1\/6 {
    left: 16%;
  }
  
  .left-2\/5 {
    left: 40%;
  }
  
  .right-1\/6 {
    right: 16%;
  }
  
  .right-2\/5 {
    right: 40%;
  }
  
  .w-4 {
    width: 1rem;
  }
  
  .h-4 {
    height: 1rem;
  }
  
  .bg-gray-800 {
    background-color: #4b5563;
  }
  
  .rounded-full {
    border-radius: 50%;
  }
  
  .-translate-y-6 {
    transform: translateY(-1.5rem);
  }
  
  .translate-y-6 {
    transform: translateY(1.5rem);
  }
  
  /* Sofa */
  .bottom-1\/6 {
    bottom: 16%;
  }
  
  .w-24 {
    width: 6rem;
  }
  
  .h-8 {
    height: 2rem;
  }
  
  .bg-blue-800 {
    background-color: #1e40af;
  }
  
  .border-blue-900 {
    border-color: #1e3a8a;
  }
  
  /* Raspberry Pi */
  .top-1\/3 {
    top: 33%;
  }
  
  .w-8 {
    width: 2rem;
  }
  
  .h-8 {
    height: 2rem;
  }
  
  .border-2 {
    border-width: 2px;
  }
  
  .flex {
    display: flex;
  }
  
  .items-center {
    align-items: center;
  }
  
  .justify-center {
    justify-content: center;
  }
  
  .group {
    position: relative;
  }
  
  .cursor-pointer {
    cursor: pointer;
  }
  
  .hover\:scale-110 {
    transition: transform 0.2s ease;
  }
  
  .group:hover .hover\:scale-110 {
    transform: scale(1.1);
  }
  
  .transition-all {
    transition: all 0.2s ease;
  }
  
  .duration-200 {
    transition-duration: 200ms;
  }
  
  .-top-8 {
    top: -2rem;
  }
  
  .bg-black\/80 {
    background-color: rgba(0, 0, 0, 0.8);
  }
  
  .text-white {
    color: #fff;
  }
  
  .text-xs {
    font-size: 0.75rem;
  }
  
  .px-2 {
    padding-left: 0.5rem;
    padding-right: 0.5rem;
  }
  
  .py-1 {
    padding-top: 0.25rem;
    padding-bottom: 0.25rem;
  }
  
  .opacity-0 {
    opacity: 0;
  }
  
  .group-hover\:opacity-100 {
    transition: opacity 0.2s ease;
  }
  
  .group:hover .group-hover\:opacity-100 {
    opacity: 1;
  }
  
  .transition-opacity {
    transition: opacity 0.2s ease;
  }
  
  .whitespace-nowrap {
    white-space: nowrap;
  }
  
  /* Devices */
  .w-6 {
    width: 1.5rem;
  }
  
  .h-6 {
    height: 1.5rem;
  }
  
  .-bottom-6 {
    bottom: -1.5rem;
  }
  
  .text-black {
    color: #000;
  }
  
  .py-0\.5 {
    padding-top: 0.125rem;
    padding-bottom: 0.125rem;
  }
  
  .font-semibold {
    font-weight: 600;
  }
  
  .w-3 {
    width: 0.75rem;
  }
  
  .h-3 {
    height: 0.75rem;
  }
  
  /* Control Panel */
  .space-y-6 > * + * {
    margin-top: 1.5rem;
  }
  
  /* Equipment Status */
  .space-y-3 > * + * {
    margin-top: 0.75rem;
  }
  
  .justify-between {
    justify-content: space-between;
  }
  
  .text-sm {
    font-size: 0.875rem;
  }
  
  /* Permit Join Panel */
  .animate-pulse {
    animation: pulse 2s infinite;
  }
  
  .space-y-4 > * + * {
    margin-top: 1rem;
  }
  
  .text-center {
    text-align: center;
  }
  
  .text-3xl {
    font-size: 1.875rem;
    font-weight: 800;
  }
  
  .w-12 {
    width: 3rem;
  }
  
  .h-12 {
    height: 3rem;
  }
  
  .mx-auto {
    margin-left: auto;
    margin-right: auto;
  }
  
  .py-3 {
    padding-top: 0.75rem;
    padding-bottom: 0.75rem;
  }
  
  .px-6 {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
  
  .shadow-lg {
    box-shadow: var(--shadow-md);
  }
  
  /* Network Status */
  .w-3 {
    width: 0.75rem;
  }
  
  .h-3 {
    height: 0.75rem;
  }
  
  .space-x-3 > * + * {
    margin-left: 0.75rem;
  }

  /* local-details.component.css */
.local-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.device-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
  margin-top: 20px;
}

.device-card {
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 15px;
  background: white;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.device-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.device-status {
  padding: 3px 8px;
  border-radius: 12px;
  font-size: 0.8rem;
  background: #ff6b6b;
  color: white;
}

.device-status.online {
  background: #51cf66;
}

.device-properties p {
  margin: 5px 0;
}

.device-footer {
  margin-top: 10px;
  font-size: 0.8rem;
  color: #666;
}
  
  /* Animations */
  @keyframes pulse {
    0% {
      box-shadow: 0 0 0 0 rgba(73, 179, 142, 0.5);
    }
    70% {
      box-shadow: 0 0 0 12px rgba(73, 179, 142, 0);
    }
    100% {
      box-shadow: 0 0 0 0 rgba(73, 179, 142, 0);
    }
  }
  
  /* Responsive Design */
  @media (min-width: 1280px) {
    .xl\:grid-cols-3 {
      grid-template-columns: 2fr 1fr;
    }
  }
  
  @media (max-width: 1279px) {
    .grid {
      grid-template-columns: 1fr;
    }
  }