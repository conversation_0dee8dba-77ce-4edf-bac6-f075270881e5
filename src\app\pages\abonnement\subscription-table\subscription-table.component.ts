import { Component, Input, Output, EventEmitter, SimpleChanges } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatIconModule } from '@angular/material/icon';
import { Subscription } from '../admin-subscription-dashboard/admin-subscription-dashboard.component';

interface ActionEvent {
  subscription: Subscription;
  action: string;
}

interface StatusConfig {
  color: string;
  icon: string;
  label: string;
}

@Component({
  selector: 'app-subscription-table',
  templateUrl: './subscription-table.component.html',
  styleUrls: ['./subscription-table.component.css'],
  standalone: true,
  imports: [CommonModule, MatIconModule] // ✅ Use Material Icons
})
export class SubscriptionTableComponent {
  @Input() subscriptions: Subscription[] = [];
  @Input() getAvailableActions: (status: string) => { type: string; label: string; icon: string; color: string }[] = () => [];
  @Output() action = new EventEmitter<ActionEvent>();

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['subscriptions']) {
      console.log('Received subscriptions in table:', this.subscriptions);
    }
  }

  emitAction(subscription: Subscription, actionType: string): void {
    console.log('Emitting action:', { subscription, actionType });
    this.action.emit({ subscription, action: actionType });
  }

  formatDate(dateString: string): string {
    return new Date(dateString).toLocaleDateString('fr-FR');
  }

  getStatusConfig(status: string): StatusConfig {
    const configs: { [key: string]: StatusConfig } = {
      active: { color: 'status-active', icon: 'check_circle', label: 'Actif' },
      paused: { color: 'status-paused', icon: 'schedule', label: 'En pause' },
      inactive: { color: 'status-inactive', icon: 'cancel', label: 'Inactif' },
      banned: { color: 'status-banned', icon: 'person_off', label: 'Banni' }
    };
    return configs[status] || configs['inactive'];
  }
}
