import { Site } from "./site";
import { Transaction } from "./transaction";
import { TypeLocal } from "./typeLocal";


export interface Local {
    nom: string;
    description: string;
    baseTopic: string;
    etage: number;
    surface: number; // In m²
    nombreCapteurs: number;
    capacitePersonnes: number;
    architecture2DImage: string; // byte[] in C# typically maps to string (base64) in TS for images
    imageLocal: string; // byte[] in C# typically maps to string (base64) in TS for images
    idSite: string; 
    site: Site;
    typeLocals: TypeLocal[];
    transactions: Transaction[];
}


