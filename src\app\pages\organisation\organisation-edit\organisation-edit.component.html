<div class="client-edit-container">
  <div class="breadcrumb-nav">
    <button class="back-button" (click)="goBack()">
      <i class="material-icons">arrow_back</i>
    </button>
    <span class="breadcrumb-text">Modification de {{client?.NomComplet}}</span>
  </div>
  
  <div class="loading-container" *ngIf="isLoading">
    <div class="spinner"></div>
    <p>Chargement des données...</p>
  </div>
  
  <div class="edit-form-container" *ngIf="!isLoading && client">
    <div class="form-card">
      <h2 class="form-title">Modifier {{client.NomComplet}}</h2>
      
      <div class="current-logo" *ngIf="client.logoOrganisation">
        <img [src]="client.logoOrganisation" alt="Logo actuel" class="logo-preview">
        <p class="logo-caption">Logo actuel</p>
      </div>
      
      <form [formGroup]="editForm" (ngSubmit)="onSubmit()">
        <div class="form-grid">
          <!-- <div class="form-group">
            <label for="id">ID Client</label>
            <input id="id" type="text" formControlName="id" class="form-control" readonly>
          </div> -->
          
          <div class="form-group">
            <label for="nomComplet">Nom Complet <span class="required">*</span></label>
            <input id="nomComplet" type="text" formControlName="nomComplet" class="form-control" required>
            <div class="form-error" *ngIf="editForm.get('nomComplet')?.hasError('required') && editForm.get('nomComplet')?.touched">
              Le nom complet est requis
            </div>
          </div>
          
          <div class="form-group">
            <label for="email">Email <span class="required">*</span></label>
            <input id="email" type="email" formControlName="email" class="form-control" required>
            <div class="form-error" *ngIf="editForm.get('email')?.hasError('required') && editForm.get('email')?.touched">
              L'email est requis
            </div>
            <div class="form-error" *ngIf="editForm.get('email')?.hasError('email') && editForm.get('email')?.touched">
              Veuillez saisir un email valide
            </div>
          </div>
          
          <div class="form-group">
            <label for="telephone">Téléphone <span class="required">*</span></label>
            <input id="telephone" type="text" formControlName="telephone" class="form-control" required>
            <div class="form-error" *ngIf="editForm.get('telephone')?.hasError('required') && editForm.get('telephone')?.touched">
              Le téléphone est requis
            </div>
          </div>
          
          <div class="form-group">
            <label for="idOrganisation">Organisation <span class="required">*</span></label>
            <select id="idOrganisation" formControlName="idOrganisation" class="form-control" required>
              <option value="">Sélectionnez une organisation</option>
              <option *ngFor="let org of organisations" [value]="org.Id">
                {{ org.Nom }}
              </option>
            </select>
            <div class="form-error" *ngIf="editForm.get('idOrganisation')?.hasError('required') && editForm.get('idOrganisation')?.touched">
              Veuillez sélectionner une organisation
            </div>
          </div>
          
          <div class="form-group">
            <label for="nombreLocaux">Nombre de Locaux</label>
            <input id="nombreLocaux" type="number" formControlName="nombreLocaux" min="0" class="form-control">
          </div>
          
          <div class="form-group">
            <label for="nombreEquipementsActif">Nombre d'Équipements Actifs</label>
            <input id="nombreEquipementsActif" type="number" formControlName="nombreEquipementsActif" min="0" class="form-control">
          </div>
          
          <div class="form-group">
            <label for="nombreEquipementsInactif">Nombre d'Équipements Inactifs</label>
            <input id="nombreEquipementsInactif" type="number" formControlName="nombreEquipementsInactif" min="0" class="form-control">
          </div>
          
          <div class="form-group checkbox-group">
            <label class="checkbox-label">
              <input type="checkbox" id="isEnabled" formControlName="isEnabled">
              <span class="checkmark"></span>
              Client actif
            </label>
          </div>
          
          <div class="form-group full-width">
            <label for="logo">Nouveau logo</label>
            <div class="file-input-container">
              <button type="button" class="file-button" (click)="fileInput.click()">
                <i class="material-icons">upload_file</i> Choisir un nouveau logo
              </button>
              <input hidden type="file" #fileInput accept="image/*" (change)="onFileSelected($event)">
              <span *ngIf="uploadedLogo" class="file-info">{{uploadedLogo.name}}</span>
            </div>
          </div>
        </div>
        
        <div class="form-actions">
          <button type="button" class="btn-cancel" (click)="goBack()">Annuler</button>
          <button type="submit" class="btn-submit" [disabled]="!editForm.valid">
            <i class="material-icons">save</i> Enregistrer
          </button>
        </div>
      </form>
    </div>
  </div>
  
  <div class="error-container" *ngIf="!isLoading && !client">
    <i class="material-icons error-icon">error</i>
    <p>Impossible de trouver les détails du client</p>
    <button class="btn-primary" (click)="navigateToList()">Retour à la liste des clients</button>
  </div>
</div>