.organisation-details-container {
  max-width: 1300px;
  margin: 30px auto;
  padding: 25px;
  background: linear-gradient(145deg, #ffffff, #f9f9f9);
  border-radius: 12px;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.08);
}

/* Breadcrumb navigation */
.breadcrumb-nav {
  display: flex;
  align-items: center;
  margin-bottom: 25px;
}

.back-button {
  background: #f5f5f5;
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  cursor: pointer;
  transition: all 0.3s ease;
}

/* Add these new styles for the current images display */
.current-images-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 16px;
  margin-top: 8px;
}

.current-image-item {
  position: relative;
  width: 120px;
  height: 120px;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.current-image-preview {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.remove-image-btn {
  position: absolute;
  top: 4px;
  right: 4px;
  background: rgba(255, 255, 255, 0.9);
  border: none;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.remove-image-btn:hover {
  background: white;
  transform: scale(1.1);
}

.remove-image-btn i {
  font-size: 18px;
  color: #ef5350;
}

.file-names {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 8px;
}

.file-name {
  background: #f0f8f0;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.75rem;
  color: #2e7d32;
}

.back-button:hover {
  background: #e0e0e0;
  transform: translateX(-3px);
}

.back-button .material-icons {
  color: #555;
  font-size: 24px;
}

.breadcrumb-text {
  font-size: 16px;
  color: #666;
}

/* Loading spinner */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 250px;
  animation: fadeIn 0.5s ease-out;
}

.spinner {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: 3px solid rgba(76, 175, 80, 0.1);
  border-top-color: #4CAF50;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.loading-container p {
  color: #718096;
  font-size: 16px;
  font-style: italic;
}

/* Organization info section */
.info-section {
  display: flex;
  margin-bottom: 30px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  padding: 25px;
  animation: fadeIn 0.5s ease-out;
}

.org-images-container {
  flex: 0 0 200px;
  margin-right: 30px;
}

.logo-container {
  width: 180px;
  height: 180px;
  border-radius: 8px;
  overflow: hidden;
  background: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  position: relative; /* Add this */
}

.org-logo {
  position: absolute; /* Add this */
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: contain; /* Change from cover to contain */
  background-color: white; /* Add this */
  padding: 8px; /* Add this for some padding around the logo */
  transition: transform 0.3s ease; /* Add smooth transition for hover effect */
}

/* Optional: Add hover effect */
.logo-container:hover .org-logo {
  transform: scale(1.05);
}

.no-logo {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  background: #f0f8f0;
}

.no-logo .material-icons {
  font-size: 80px;
  color: #c5c5c5;
}

.org-info-container {
  flex: 1;
}

.org-name {
  margin: 0 0 20px;
  font-size: 28px;
  font-weight: 700;
  color: #2E7D32;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.info-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 500;
  color: #666;
}

.info-label .material-icons {
  font-size: 20px;
  color: #4CAF50;
}

.info-value {
  font-size: 16px;
  color: #333;
  font-weight: 500;
}

/* Site form */
.site-form-container {
  margin-bottom: 30px;
  animation: slideIn 0.5s ease-out;
}

.form-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  padding: 25px;
}

.form-title {
  margin: 0 0 20px;
  font-size: 22px;
  font-weight: 600;
  color: #2E7D32;
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
  gap: 20px;
  margin-bottom: 25px;
}

.form-group {
  display: flex;
  flex-direction: column;
  margin-bottom: 15px;
}

.full-width {
  grid-column: 1 / -1;
}

.form-group label {
  font-size: 14px;
  color: #4a5568;
  margin-bottom: 6px;
  font-weight: 500;
}

.form-control {
  height: 40px;
  padding: 8px 12px;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.form-control:focus {
  border-color: #4CAF50;
  box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.2);
  outline: none;
}

.form-error {
  font-size: 12px;
  color: #e53e3e;
  margin-top: 4px;
}

.file-input-container {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-top: 6px;
}

.file-button {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  background-color: #edf2f7;
  color: #4a5568;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  transition: all 0.3s ease;
  cursor: pointer;
  font-size: 14px;
}

.file-button:hover {
  background-color: #e2e8f0;
}

.file-info {
  font-size: 14px;
  color: #718096;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 15px;
}

.btn-cancel {
  padding: 10px 20px;
  background: transparent;
  border: 1px solid #cbd5e0;
  color: #718096;
  border-radius: 6px;
  transition: all 0.3s ease;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
}

.btn-cancel:hover {
  background-color: #f7fafc;
}

.btn-submit {
  padding: 10px 20px;
  background: linear-gradient(45deg, #4CAF50, #81C784);
  color: white;
  border: none;
  border-radius: 6px;
  transition: all 0.3s ease;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  box-shadow: 0 2px 6px rgba(76, 175, 80, 0.3);
}

.btn-submit:hover {
  background: linear-gradient(45deg, #81C784, #4CAF50);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(76, 175, 80, 0.4);
}

.btn-submit:disabled {
  background: #cbd5e0;
  transform: none;
  box-shadow: none;
  cursor: not-allowed;
}

/* Sites section */
.sites-section {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  padding: 25px;
  animation: fadeIn 0.5s ease-out;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  flex-wrap: wrap;
  gap: 15px;
}

.view-controls {
  display: flex;
  gap: 8px;
  background: #f5f5f5;
  border-radius: 8px;
  padding: 4px;
}

.view-toggle-btn {
  background: none;
  border: none;
  border-radius: 6px;
  padding: 8px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #666;
  transition: all 0.2s ease;
}

.view-toggle-btn.active {
  background: #ffffff;
  color: #2E7D32;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.view-toggle-btn:hover:not(.active) {
  background: rgba(255, 255, 255, 0.5);
}

.add-site-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  background: #2E7D32;
  color: white;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: background 0.2s ease;
}

.add-site-btn:hover {
  background: #1b5e20;
}

.table-container {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  margin-bottom: 30px;
}

.section-title {
  margin: 0;
  font-size: 22px;
  font-weight: 600;
  color: #2d3748;
}

.sites-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 25px;
}

.no-sites-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 50px 0;
  text-align: center;
  color: #718096;
}

.no-sites-message .material-icons {
  font-size: 48px;
  color: #cbd5e0;
  margin-bottom: 15px;
}

.no-sites-message p {
  margin: 0 0 20px;
  font-size: 16px;
}

/* Error container */
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 50px 0;
  text-align: center;
  color: #718096;
  animation: fadeIn 0.5s ease-out;
}

.error-icon {
  font-size: 48px;
  color: #e53e3e;
  margin-bottom: 15px;
}

.error-container p {
  margin: 0 0 20px;
  font-size: 16px;
}

.btn-primary {
  padding: 10px 20px;
  background: linear-gradient(45deg, #4CAF50, #81C784);
  color: white;
  border: none;
  border-radius: 6px;
  box-shadow: 0 2px 6px rgba(76, 175, 80, 0.3);
  transition: all 0.3s ease;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.btn-primary:hover {
  background: linear-gradient(45deg, #81C784, #4CAF50);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(76, 175, 80, 0.4);
}

/* Pagination Controls */
.pagination-controls {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  margin: 30px 0 10px 0;
  flex-wrap: wrap;
}

.pagination-controls button {
  min-width: 36px;
  height: 36px;
  padding: 0 10px;
  border: none;
  background: #f5f5f5;
  color: #333;
  border-radius: 6px;
  font-size: 16px;
  cursor: pointer;
  transition: background 0.2s, color 0.2s, box-shadow 0.2s;
  box-shadow: 0 1px 3px rgba(44, 62, 80, 0.04);
  outline: none;
}

.pagination-controls button.active,
.pagination-controls button:focus {
  background: linear-gradient(45deg, #4CAF50, #81C784);
  color: #fff;
  font-weight: bold;
  box-shadow: 0 2px 8px rgba(76, 175, 80, 0.15);
}

.pagination-controls button:disabled {
  background: #e0e0e0;
  color: #bdbdbd;
  cursor: not-allowed;
  box-shadow: none;
}

.pagination-controls button:hover:not(:disabled):not(.active) {
  background: #e8f5e9;
  color: #388e3c;
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive design */
@media (max-width: 768px) {
  .info-section {
    flex-direction: column;
  }
  
  .org-images-container {
    margin-right: 0;
    margin-bottom: 20px;
    align-self: center;
  }
  
  .info-grid {
    grid-template-columns: 1fr;
  }
  
  .sites-grid {
    grid-template-columns: 1fr;
  }
  
  .form-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .section-header {
    flex-direction: column;
    gap: 15px;
    align-items: flex-start;
  }
  
  .btn-add {
    width: 100%;
    justify-content: center;
  }
  
  .logo-container {
    width: 120px;
    height: 120px;
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .section-header {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .view-controls {
    width: 100%;
    justify-content: center;
  }
  
  .add-site-btn {
    width: 100%;
    justify-content: center;
  }
}
