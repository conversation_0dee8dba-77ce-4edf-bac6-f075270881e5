/* Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: var(--font-family);
  color: var(--text-primary);
  background: var(--background);
  line-height: 1.6;
  cursor: default;
}

/* Custom Cursor Animation */
button, .add-button, .tag-status {
  position: relative;
  cursor: pointer;
}

button:hover, .add-button:hover, .tag-status:hover {
  cursor: pointer;
}

button::before, .add-button::before, .tag-status::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: var(--ripple-color);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  opacity: 0;
  transition: width 0.4s ease, height 0.4s ease, opacity 0.4s ease;
  pointer-events: none;
}

button:active::before, .add-button:active::before, .tag-status:active::before {
  width: 100px;
  height: 100px;
  opacity: 0.5;
}

/* Container */
.container {
  max-width: 1280px;
  margin: 0 auto;
  padding: 32px;
  background: var(--background);
  min-height: 100vh;
  animation: fadeIn 0.5s ease-out;
}

/* Card */
.card {
  border: none;
  box-shadow: var(--shadow);
  background: var(--card-bg);
  border-radius: var(--border-radius);
  overflow: hidden;
  transition: var(--transition);
}

.card:hover {
  box-shadow: var(--shadow-hover);
}

/* Header */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px;
  border-bottom: 1px solid var(--border-color);
  background: linear-gradient(to right, var(--background), #edf2f7);
}

.title {
  font-size: 26px;
  font-weight: 700;
  color: var(--primary-color);
  letter-spacing: -0.025em;
}

/* Search and Create */
.search-create {
  padding: 24px;
  border-bottom: 1px solid var(--border-color);
  background: var(--background);
  display: flex;
  gap: 16px;
  animation: slideIn 0.5s ease-out;
}

/* Updated Input Styles */
.input {
  flex: 1;
  padding: 12px 16px;
  border: 1px solid var(--border-color);
  border-radius: 12px;
  font-size: 15px;
  font-weight: 400;
  color: var(--text-primary);
  background: var(--card-bg);
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.input:focus {
  border-color: var(--accent-color);
  box-shadow: 0 0 0 4px rgba(72, 187, 120, 0.15);
  background: #fff;
  outline: none;
}

/* Rule Item */
.rule-item {
  padding: 20px 24px;
  border-bottom: 1px solid var(--border-color);
  transition: var(--transition);
  animation: fadeInUp 0.5s ease-out;
}

.rule-item.drag-over {
  background: #f0fff4;
  border-color: #c6f6d5;
}

.rule-item.dragging {
  opacity: 0.6;
  transform: scale(0.98);
}

.rule-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 16px;
}

.rule-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.rule-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;
}

.rule-name {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
}

.priority {
  font-size: 13px;
  color: var(--text-secondary);
  background: rgba(0, 0, 0, 0.03);
  padding: 2px 8px;
  border-radius: 6px;
}

.status {
  padding: 4px 12px;
  border-radius: 9999px;
  font-size: 12px;
  font-weight: 500;
  transition: var(--transition);
}

.status-active {
  background: #e6fffa;
  color: var(--primary-color);
}

.status-inactive {
  background: #fed7d7;
  color: #c53030;
}

/* Tags */
.tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.tag-container {
  display: flex;
  align-items: center;
  gap: 4px;
}

.tag {
  padding: 4px 12px;
  border-radius: 9999px;
  font-size: 12px;
  font-weight: 500;
  transition: var(--transition);
}

.tag-active {
  background: #e6fffa;
  color: #2b6cb0;
}

.tag-inactive {
  background: #edf2f7;
  color: var(--text-secondary);
}

/* Updated Tag Status Button */
.tag-status {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 11px;
  font-weight: 600;
  color: white;
  border: none;
  transition: all 0.3s ease;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.tag-status-active {
  background: linear-gradient(145deg, var(--accent-color), #34d399);
}

.tag-status-inactive {
  background: linear-gradient(145deg, var(--text-secondary), #a0aec0);
}

.tag-status:hover {
  transform: scale(1.15) rotate(10deg);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.tag-status:active {
  transform: scale(0.95);
}

/* Actions */
.actions {
  display: flex;
  gap: 8px;
}

/* Modal */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: flex-start;
  justify-content: center;
  padding: 16px;
  z-index: 1000;
  overflow-y: auto;
  animation: fadeIn 0.3s ease-out;
}

.modal-content {
  background: var(--card-bg);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
  width: 100%;
  max-width: 896px;
  margin: 32px 0;
  animation: slideUp 0.4s ease-out;
  backdrop-filter: blur(5px);
}

.modal-header {
  background: linear-gradient(to right, var(--background), #edf2f7);
  padding: 20px 24px;
  border-bottom: 1px solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-title {
  font-size: 24px;
  font-weight: 700;
  color: var(--text-primary);
}

/* Modal Body and Footer */
.modal-body {
  padding: 24px;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 20px 24px;
  border-top: 1px solid var(--border-color);
}

/* Updated Button Styles */
button.mat-raised-button {
  padding: 10px 24px;
  border-radius: 12px;
  font-weight: 600;
  font-size: 14px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  border: none;
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.1);
}

/* Specific styling for the toggle order button in the header */
.header button.mat-raised-button {
  background: var(--primary-dark); /* Default background for both states */
  color: white;
}

.header button.mat-raised-button.mat-primary {
  background: var(--primary-dark); /* Ensure mat-primary keeps the same background */
}

.header button.mat-raised-button:hover {
  background: var(--primary-dark); /* Maintain base color on hover */
  opacity: 0.9; /* Slight fade for hover effect */
  box-shadow: 0 5px 12px rgba(47, 133, 90, 0.3);
  transform: translateY(-2px);
}

.header button.mat-raised-button:active {
  transform: scale(0.97);
  box-shadow: 0 2px 6px rgba(47, 133, 90, 0.2);
}

.header button.mat-raised-button::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: width 0.4s ease, height 0.4s ease;
}

.header button.mat-raised-button:hover::after {
  width: 250px;
  height: 250px;
}

/* Reset other mat-raised-button styles to avoid overriding */
button.mat-raised-button:not(.header button.mat-raised-button) {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--accent-color) 100%);
  color: white;
}

button.mat-raised-button:not(.header button.mat-raised-button):hover {
  background: linear-gradient(135deg, var(--primary-hover) 0%, var(--accent-hover) 100%);
  box-shadow: 0 5px 12px rgba(47, 133, 90, 0.3);
  transform: translateY(-2px);
}

button.mat-raised-button:not(.header button.mat-raised-button):active {
  transform: scale(0.97);
  box-shadow: 0 2px 6px rgba(47, 133, 90, 0.2);
}

button.mat-raised-button:not(.header button.mat-raised-button)::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: width 0.4s ease, height 0.4s ease;
}

button.mat-raised-button:not(.header button.mat-raised-button):hover::after {
  width: 250px;
  height: 250px;
}

button[mat-icon-button] {
  width: 44px;
  height: 44px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  background: transparent;
  border: none;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

button[mat-icon-button].mat-warn {
  color: var(--error-color);
  background: rgba(229, 62, 62, 0.08);
}

button[mat-icon-button]:hover {
  transform: scale(1.1);
  background: rgba(0, 0, 0, 0.03);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

button[mat-icon-button]:active {
  transform: scale(0.95);
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
}

button.mat-button {
  padding: 8px 16px;
  border-radius: 10px;
  font-weight: 500;
  font-size: 14px;
  color: var(--text-primary);
  background: transparent;
  border: 1px solid var(--border-color);
  transition: all 0.3s ease;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
}

button.mat-button:hover {
  background: rgba(0, 0, 0, 0.03);
  border-color: var(--accent-color);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

button.mat-button:active {
  transform: scale(0.97);
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
}

/* Icons */
mat-icon {
  transition: all 0.3s ease;
  font-size: 20px;
  width: 20px;
  height: 20px;
}

button:hover mat-icon {
  transform: scale(1.15);
}

button:active mat-icon {
  transform: rotate(10deg);
}

/* Form Group */
.form-group {
  background: var(--card-bg);
  padding: 20px;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
  border: 1px solid var(--border-color);
  transition: var(--transition);
}

.form-group:hover {
  box-shadow: var(--shadow-hover);
}

.form-group-header {
  background: var(--background);
  margin: -20px -20px 20px -20px;
  padding: 16px 20px;
  border-radius: var(--border-radius) var(--border-radius) 0 0;
  border-bottom: 1px solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.form-label {
  font-size: 14px;
  font-weight: 600;
  color: var(--text-primary);
}

/* Updated Inputs and Selects */
.input,
.select {
  padding: 12px 16px;
  border: 1px solid var(--border-color);
  border-radius: 12px;
  font-size: 15px;
  font-weight: 400;
  color: var(--text-primary);
  background: var(--card-bg);
  transition: all 0.3s ease;
  width: 100%;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.05);
}

.input:focus,
.select:focus {
  border-color: var(--accent-color);
  box-shadow: 0 0 4px 6px rgba(72, 255, 120, 0.1);
  background: #fff;
  outline: none;
}

/* Specific Styling for Value Input */
.input-value {
  min-width: 150px; /* Ensure a minimum width for visibility */
  max-width: 200px; /* Prevent it from growing too large */
  overflow: visible; /* Ensure content isn’t clipped */
  white-space: normal; /* Allow text wrapping if needed */
  text-overflow: clip; /* Prevent ellipsis hiding content */
}

/* Condition and Action Rows */
.condition-row,
.action-row {
  background: #f8fafc;
  padding: 16px;
  border-radius: 8px;
  margin-bottom: 16px;
  border: 1px solid var(--border-color);
  display: flex;
  gap: 12px;
  align-items: center;
  transition: var(--transition);
  flex-wrap: wrap; /* Allow wrapping to prevent overflow */
}

.condition-row:hover,
.action-row:hover {
  background: var(--card-bg);
  box-shadow: var(--shadow);
  transform: translateY(-2px);
}

.condition-and,
.action-and {
  background: #e6fffa;
  color: var(--primary-color);
  padding: 6px 14px;
  border-radius: 16px;
  font-weight: 600;
  font-size: 12px;
  transition: var(--transition);
}

.condition-and:hover,
.action-and:hover {
  background: #c6f6d5;
  transform: scale(1.05);
}

/* Updated Add Button */
.add-button {
  padding: 8px 16px;
  border-radius: 8px;
  background: linear-gradient(45deg, rgba(72, 187, 120, 0.1), rgba(72, 204, 120, 0.05));
  color: var(--accent-color);
  font-size: 14px;
  font-weight: 600;
  border: 1px solid rgba(72, 187, 120, 0.3);
  transition: all 0.3s ease;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
}

.add-button:hover {
  background: linear-gradient(45deg, rgba(72, 187, 120, 0.2), rgba(72, 204, 120, 0.1));
  color: var(--accent-hover);
  border-color: var(--accent-color);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.add-button:active {
  transform: scale(0.97);
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
}

/* Medium Width Select for Type Dropdown */
.select-medium {
  width: 200px;
  min-width: 200px;
  max-width: 200px;
}

/* Preview Group */
.preview-group {
  background: linear-gradient(to right, var(--background), #edf2f7);
  border: none;
  box-shadow: var(--shadow);
  padding: 20px;
  border-radius: var(--border-radius);
  margin-bottom: 24px;
  transition: var(--transition);
}

.preview-group:hover {
  box-shadow: var(--shadow-hover);
}

.preview-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 12px;
}

.preview-content {
  background: var(--card-bg);
  border-radius: 8px;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.05);
  padding: 16px;
}

.preview-json {
  padding: 16px;
  border-radius: 6px;
  background: #1a202c;
  color: #f7fafc;
  font-family: 'Fira Code', 'Consolas', monospace;
  font-size: 13px;
  white-space: pre-wrap;
  overflow-x: auto;
  line-height: 1.6;
  transition: var(--transition);
}

.preview-json .key {
  color: #63b3ed;
}

.preview-json .string {
  color: #68d391;
}

.preview-json .number {
  color: #f6e05e;
}

.preview-json .boolean {
  color: #ed64a6;
}

.order-button {
  background-color: #199546 !important; /* Light green */
  color: white !important;
  transition: background-color 0.3s ease;
}

.order-button-active {
  background-color: #199546 !important; /* Dark green */
  color: white !important;
  transition: background-color 0.3s ease;
}

.order-button:hover, .order-button-active:hover {
  opacity: 0.9;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.create-button {
  background-color: #41792e !important;
  color: white !important;
  transition: all 0.3s ease;
}

.create-button:hover {
  background-color: #41792e !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.create-button:active {
  background-color: #325725 !important;
  transform: translateY(1px);
}

.create-button .mat-icon {
  margin-right: 8px;
}

/* Animations */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes fadeInUp {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes slideIn {
  from { transform: translateX(-20px); opacity: 0; }
  to { transform: translateX(0); opacity: 1; }
}

@keyframes slideUp {
  from { transform: translateY(20px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

/* Responsive Design */
@media (max-width: 768px) {
  .container {
    padding: 16px;
  }

  .modal-content {
    margin: 16px;
    max-width: 100%;
  }

  .rule-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .actions {
    margin-top: 8px;
  }

  .condition-row,
  .action-row {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }

  .condition-and,
  .action-and {
    margin-bottom: 8px;
    align-self: flex-start;
  }

  .input,
  .select,
  .select-medium,
  .input-value {
    width: 100%;
    max-width: 100%; /* Override max-width on smaller screens */
  }

  .modal-header,
  .modal-footer {
    padding: 16px;
  }
}

@media (max-width: 480px) {
  .title,
  .modal-title {
    font-size: 20px;
  }

  .search-create {
    flex-direction: column;
    gap: 12px;
  }

  .preview-json {
    font-size: 12px;
  }

  .rule-header {
    flex-wrap: wrap;
    gap: 8px;
  }

  button.mat-raised-button {
    padding: 8px 12px;
    font-size: 12px;
  }
}