<div id="section" class="header-section">
    <div class="page-title">
      <h1 class="title">
        <!-- <i class="material-icons title-icon">business</i> {{ selectedType ? " Tous les " +  selectedType + "s" : "Gestion des Organisations" }} -->
      </h1>
      <p class="subtitle">
        Visualisez et gérez vos organisations avec facilité
      </p>
    </div>

    <div class="actions">
      <button
        class="create-button"
        (click)="showAddOrganisationForm()"
        *ngIf="!showCreateForm"
      >
        <i class="material-icons action-icon">add</i> Créer Organisation
      </button>
      <button class="view-toggle" (click)="toggleViewMode()">
        <i class="material-icons action-icon">{{
          viewMode === "cards" ? "view_list" : "grid_view"
        }}</i>
        {{ viewMode === "cards" ? "Vue Tableau" : "Vue Cartes" }}
      </button>
    </div>
  </div>

  <!-- <div class="create-form-card" *ngIf="showCreateForm" [@fadeIn]>
    <h2 class="form-title">Nouvelle Organisation</h2>
    <form [formGroup]="createOrganisationForm" (ngSubmit)="submitCreateForm()">
      <div class="form-grid">
        <div class="form-group">
          <label for="nom">Nom*</label>
          <input
            id="nom"
            type="text"
            formControlName="nom"
            required
            class="form-control"
          />
          <div
            class="form-error"
            *ngIf="
              createOrganisationForm.get('nom')?.invalid &&
              createOrganisationForm.get('nom')?.touched
            "
          >
            <span
              *ngIf="createOrganisationForm.get('nom')?.hasError('required')"
              >Le nom est requis</span
            >
            <span
              *ngIf="createOrganisationForm.get('nom')?.hasError('minlength')"
              >Le nom doit contenir au moins 2 caractères</span
            >
          </div>
        </div>

        <div class="form-group">
          <label for="type">Type d'organisation*</label>
          <select
            id="type"
            formControlName="type"
            required
            class="form-control"
          >
            <option value="" disabled>Sélectionner un type</option>
            <option *ngFor="let type of organisationTypes" [value]="type.value">
              {{ type.label }}
            </option>
          </select>
          <div
            class="form-error"
            *ngIf="
              createOrganisationForm.get('type')?.invalid &&
              createOrganisationForm.get('type')?.touched
            "
          >
            Le type d'organisation est requis
          </div>
        </div>

        <div class="form-group">
          <label for="emailAddress">Email</label>
          <input
            id="emailAddress"
            type="email"
            formControlName="emailAddress"
            class="form-control"
          />
          <div
            class="form-error"
            *ngIf="
              createOrganisationForm.get('emailAddress')?.hasError('email')
            "
          >
            Veuillez saisir un email valide
          </div>
        </div>

        <div class="form-group">
          <label for="nombreEmployees">Nombre d'employés</label>
          <input
            id="nombreEmployees"
            type="number"
            formControlName="nombreEmployees"
            min="0"
            class="form-control"
          />
          <div
            class="form-error"
            *ngIf="
              createOrganisationForm.get('nombreEmployees')?.hasError('min')
            "
          >
            Le nombre d'employés ne peut pas être négatif
          </div>
        </div>

        <div class="form-group">
          <label for="nombreEquipement">Nombre d'équipements</label>
          <input
            id="nombreEquipement"
            type="number"
            formControlName="nombreEquipement"
            min="0"
            class="form-control"
          />
          <div
            class="form-error"
            *ngIf="
              createOrganisationForm.get('nombreEquipement')?.hasError('min')
            "
          >
            Le nombre d'équipements ne peut pas être négatif
          </div>
        </div>

        <div class="form-group">
          <label for="latitude">Latitude*</label>
          <input
            id="latitude"
            type="number"
            formControlName="latitude"
            step="0.000001"
            class="form-control"
            required
          />
          <div
            class="form-error"
            *ngIf="
              createOrganisationForm.get('latitude')?.invalid &&
              createOrganisationForm.get('latitude')?.touched
            "
          >
            La latitude est requise
          </div>
        </div>

        <div class="form-group">
          <label for="longitude">Longitude*</label>
          <input
            id="longitude"
            type="number"
            formControlName="longitude"
            step="0.000001"
            class="form-control"
            required
          />
          <div
            class="form-error"
            *ngIf="
              createOrganisationForm.get('longitude')?.invalid &&
              createOrganisationForm.get('longitude')?.touched
            "
          >
            La longitude est requise
          </div>
        </div>

        <div class="form-group">
          <label for="consommation">Consommation (kWh)</label>
          <input
            id="consommation"
            type="number"
            formControlName="consommation"
            min="0"
            step="0.01"
            class="form-control"
          />
          <div
            class="form-error"
            *ngIf="createOrganisationForm.get('consommation')?.hasError('min')"
          >
            La consommation ne peut pas être négative
          </div>
        </div>

        <div class="form-group">
          <label for="logo">Logo</label>
          <div class="file-input-container">
            <button
              type="button"
              class="file-button"
              (click)="fileInput.click()"
            >
              <i class="material-icons">upload_file</i> Choisir un logo
            </button>
            <input
              hidden
              type="file"
              #fileInput
              accept="image/*"
              (change)="onFileSelected($event)"
            />
            <span *ngIf="uploadedLogo" class="file-info">
              {{ uploadedLogo.name }}
            </span>
          </div>
        </div>
      </div>

      <div class="form-actions">
        <button
          type="button"
          class="btn-cancel"
          (click)="hideAddOrganisationForm()"
        >
          Annuler
        </button>
        <button
          type="submit"
          class="btn-submit"
          [disabled]="!createOrganisationForm.valid"
        >
          Créer
        </button>
      </div>
    </form>
  </div> -->

  <!-- <div class="search-bar">
    <div class="search-field">
      <i class="material-icons search-icon">search</i>
      <input
        type="text"
        [(ngModel)]="searchTerm"
        (keyup)="filterOrganisations()"
        placeholder="Rechercher une organisation"
      />
      <button
        *ngIf="searchTerm"
        (click)="searchTerm = ''; filterOrganisations()"
        class="clear-button"
      >
        <i class="material-icons">close</i>
      </button>
    </div>
    <div class="active-filters" *ngIf="searchOrganisationType">
      <span class="active-filter">
        Afficher Tout
        <button (click)="clearTypeFilter()" class="clear-filter">
          <i class="material-icons">close</i>
        </button>
      </span>
    </div>
  </div> -->

  <div class="loading-container" *ngIf="isLoading">
    <div class="spinner"></div>
    <p>Chargement des organisations...</p>
  </div>

  <div class="no-data" *ngIf="!isLoading && filteredOrganisations.length === 0">
    <i class="material-icons">warning</i>
    <p>Aucune organisation trouvée</p>
  </div>

  <!-- Cards View -->
<div class="cards-view-container" *ngIf="viewMode === 'cards' && !isLoading && filteredOrganisations.length > 0">
  <div class="cards-container">
    <app-card-organisation 
      *ngFor="let org of filteredOrganisations" 
      [organisation]="org"
      (viewDetails)="viewOrganisationDetails($event)"
      (editOrg)="editOrganisation($event)"
      (deleteOrg)="deleteOrganisation($event)"
      [@fadeIn]>
    </app-card-organisation>
  </div>
    <div class="pagination-controls" *ngIf="viewMode === 'cards'">
      <button
        class="pagination-button"
        [disabled]="currentPage === 1"
        (click)="onPageChange(currentPage - 1)"
      >
        <i class="material-icons">chevron_left</i>
      </button>

      <div class="page-numbers">
        <button
          *ngFor="let page of getPageNumbers()"
          class="page-number-button"
          [class.active]="page === currentPage"
          [class.separator]="page === -1"
          [disabled]="page === -1"
          (click)="page !== -1 && onPageChange(page)"
        >
          {{ page === -1 ? "..." : page }}
        </button>
      </div>

    <button 
      class="pagination-button"
      [disabled]="currentPage === totalPages"
      (click)="onPageChange(currentPage + 1)">
      <i class="material-icons">chevron_right</i>
    </button>
  </div>
</div>
<div class="table-container" *ngIf="viewMode === 'table' && !isLoading && filteredOrganisations.length > 0">
    <table class="data-table">
      <thead>
        <tr>
          <th>Nom</th>
          <th>Type</th>
          <th>Email</th>
          <th>Employés</th>
          <th>Équipements</th>
          <th>Consommation</th>
          <th>Actions</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let org of filteredOrganisations" [@tableRowAnimation]>
          <td>{{org.nom}}</td>
          <td>{{getOrganisationTypeLabel(org.type)}}</td>
          <td>{{org.emailAddress || '-'}}</td>
          <td>{{org.nombreEmployees}}</td>
          <td>{{org.nombreEquipement}}</td>
          <td>{{org.consommation}} kWh</td>
          <td class="actions-cell">
            <button class="action-button view" (click)="viewOrganisationDetails(org.id)" title="Voir détails">
              <i class="material-icons">visibility</i>
            </button>
            <button class="action-button edit" (click)="editOrganisation(org.id)" title="Modifier">
              <i class="material-icons">edit</i>
            </button>
            <button class="action-button delete" (click)="deleteOrganisation(org.id)" title="Supprimer">
              <i class="material-icons">delete</i>
            </button>
          </td>
        </tr>
      </tbody>
    </table>

    <div class="pagination-controls" *ngIf="viewMode === 'table'">
    <button 
      class="pagination-button"
      [disabled]="currentPage === 1"
      (click)="onPageChange(currentPage - 1)">
      <i class="material-icons">chevron_left</i>
    </button>

    <div class="page-numbers">
      <button 
        *ngFor="let page of getPageNumbers()"
        class="page-number-button"
        [class.active]="page === currentPage"
        [class.separator]="page === -1"
        [disabled]="page === -1"
        (click)="page !== -1 && onPageChange(page)">
        {{ page === -1 ? '...' : page }}
      </button>
    </div>

    <button 
      class="pagination-button"
      [disabled]="currentPage === totalPages"
      (click)="onPageChange(currentPage + 1)">
      <i class="material-icons">chevron_right</i>
    </button>
  </div>
  </div>