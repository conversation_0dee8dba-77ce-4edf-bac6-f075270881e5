import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { ApiService } from '../api.service';
import { Site } from '@app/core/models/site';
import { map, catchError } from 'rxjs/operators';
import { environment } from '@app/environments/environment';

@Injectable({ providedIn: 'root' })
export class SiteApiService extends ApiService<Site> {

  bbaseUrl:string = environment.host.endsWith('/')
      ? environment.host + 'api/'
      : environment.host + '/api/';
  constructor(public override http: HttpClient) {
    super(http);
    this.setEndpoint("site");
  }

  // Add a method to get paginated sites
  getPaginated(page: number, pageSize: number) {
    const params = {
      pagination: {
        currentPage: page,
        pageSize: pageSize
      }
    };
    
    return this.http.post<any>(`${this.bbaseUrl}site/search`, params)
      .pipe(
        map(response => {
          return {
            sites: response.sites ?? response.data ?? [],
            totalCount: response.totalCount ?? response.total ?? 0,
            currentPage: response.currentPage ?? page,
            pageSize: response.pageSize ?? pageSize
          };
        }),
        
      );
  }

  searchPaginated(searchTerm: string, page: number, pageSize: number) {
    const params = {
      pagination: {
        currentPage: page,
        pageSize: pageSize
      },
      search: searchTerm
    };
    
    return this.http.post<any>(`${this.bbaseUrl}site/search`, params)
      .pipe(
        map(response => {
          return {
            sites: response.sites ?? response.data ?? [],
            totalCount: response.totalCount ?? response.total ?? 0,
            currentPage: response.currentPage ?? page,
            pageSize: response.pageSize ?? pageSize
          };
        }),
        
      );
  }
}
