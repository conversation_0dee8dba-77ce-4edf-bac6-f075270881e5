<div class="container">
  <div class="card">
    <div class="header">
      <h1 class="title">Gestionnaire de Règles</h1>
      <button
        mat-raised-button
        [ngClass]="{
          'order-button-active': isDragMode,
          'order-button': !isDragMode
        }"
        (click)="toggleDragMode()"
      >
        {{ isDragMode ? "Sauvegarder l'ordre" : "Modifier l'ordre" }}
      </button>
    </div>

    <!-- Search and Create -->
    <div class="search-create">
      <input type="text" placeholder="Nom de la règle" class="input" />
      <button
        mat-raised-button
        class="create-button"
        (click)="showCreatePanel = true"
      >
        <mat-icon class="mat-icon">add</mat-icon> Créer une nouvelle règle
      </button>
    </div>

    <!-- Rules List -->
    <div
      cdkDropList
      [cdkDropListDisabled]="!isDragMode"
      (cdkDropListDropped)="drop($event)"
    >
      <div
        *ngFor="let rule of rules; let i = index"
        cdkDrag
        class="rule-item"
        [ngClass]="{ 'drag-over': false, dragging: false }"
      >
        <div class="rule-content">
          <mat-icon
            class="mat-icon drag-handle"
            *ngIf="isDragMode"
            cdkDragHandle
            >drag_indicator</mat-icon
          >
          <div class="rule-details">
            <div class="rule-header">
              <h3 class="rule-name">{{ rule.name }}</h3>
              <span class="priority">Priorité: {{ rule.priority }}</span>
              <span
                class="status"
                [ngClass]="{
                  'status-active': rule.status === 'active',
                  'status-inactive': rule.status === 'inactive'
                }"
              >
                {{ rule.status === "active" ? "Active" : "Inactive" }}
              </span>
            </div>
            <div class="tags">
              <div *ngFor="let tag of rule.tags" class="tag-container">
                <span
                  class="tag"
                  [ngClass]="{
                    'tag-inactive': rule.status === 'inactive',
                    'tag-active': rule.status !== 'inactive'
                  }"
                >
                  {{ tag }}
                </span>
                <button
                  class="tag-status"
                  [ngClass]="{
                    'tag-status-active':
                      rule.tagStatus[tag] === 'active' &&
                      rule.status !== 'inactive',
                    'tag-status-inactive':
                      rule.tagStatus[tag] !== 'active' ||
                      rule.status === 'inactive'
                  }"
                  [disabled]="rule.status === 'inactive'"
                  (click)="toggleTagStatus(rule.id, tag)"
                >
                  {{
                    rule.status === "inactive"
                      ? "×"
                      : rule.tagStatus[tag] === "active"
                      ? "✓"
                      : "×"
                  }}
                </button>
              </div>
            </div>
          </div>
          <div class="actions" *ngIf="!isDragMode">
            <button
              mat-icon-button
              [color]="rule.status === 'active' ? 'primary' : 'warn'"
              (click)="toggleRuleStatus(rule.id)"
            >
              <mat-icon class="mat-icon">{{
                rule.status === "active" ? "check" : "warning"
              }}</mat-icon>
            </button>
            <button mat-icon-button (click)="openEditPanel(rule)">
              <mat-icon class="mat-icon">edit</mat-icon>
            </button>
            <button
              mat-icon-button
              color="warn"
              (click)="confirmDeleteRule(rule.id)"
            >
              <mat-icon class="mat-icon">delete</mat-icon>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Create Rule Panel -->
  <div class="modal" *ngIf="showCreatePanel">
    <div class="modal-content">
      <div class="modal-header">
        <h2 class="modal-title">Créer une nouvelle règle</h2>
        <button mat-icon-button (click)="showCreatePanel = false">
          <mat-icon class="mat-icon">close</mat-icon>
        </button>
      </div>
      <div class="modal-body">
        <!-- Rule Name -->
        <div class="form-group">
          <label class="form-label">Nom de la règle</label>
          <input
            type="text"
            [(ngModel)]="newRule.name"
            class="input"
            placeholder="Ex: Éteindre les lumières quand luminosité élevée"
          />
        </div>

        <!-- Tags -->
        <div class="form-group">
          <label class="form-label">Modèle de sujet de déclenchement</label>
          <input
            type="text"
            [(ngModel)]="newRule.tags"
            class="input"
            placeholder=""
          />
        </div>

        <!-- Tags -->
        <div class="form-group">
          <label class="form-label">Tags (ex: #france #south)</label>
          <input
            type="text"
            [(ngModel)]="newRule.tags"
            class="input"
            placeholder="#france #south #bedroom"
          />
        </div>

        <div class="form-group">
          <input
            type="checkbox"
            [(ngModel)]="newRule.tags"
            class=""
            placeholder="#france #south #bedroom"
            style="margin-right: 10px; margin-top: 5px"
          />
          <label class="form-label">Règle Active ?</label>
        </div>

        <!-- Tags -->
        <div class="form-group">
          <label class="form-label">Priorité</label>
          <input
            type="number"
            [(ngModel)]="newRule.tags"
            class="input"
            placeholder="1 (1 = haute priorité, 10 = basse priorité)"
          />
        </div>

        <!-- Configuration de l’horaire -->
        <div class="form-group">
          <div class="form-group-header">
            <label class="form-label" style="width: 100%"
              >Configuration de l'horaire</label
            >
            <div class="form-group">
              <input
                type="checkbox"
                class="input"
                placeholder="#france #south #bedroom"
                style="margin-right: 10px; margin-top: 5px; width: 40px;"
              />
            </div>
          </div>
        </div>

        <!-- Conditions -->
        <div class="form-group">
          <div class="form-group-header">
            <label class="form-label">Conditions (SI)</label>
            <button class="add-button" (click)="addCondition()">
              + Ajouter une condition
            </button>
          </div>
          <div
            *ngFor="let condition of newRule.conditions; let i = index"
            class="condition-row"
          >
            <span *ngIf="i > 0" class="condition-and">ET</span>
            <select
              (change)="handleInputChange($event, i, 'type', true)"
              class="select select-medium"
            >
              <option value="">Type d'entrée...</option>
              <option value="payload">Payload</option>
              <option value="time">Time</option>
              <option value="sensor_data">Sensor Data</option>
            </select>
            <select
              [(ngModel)]="condition.type"
              (change)="handleInputChange($event, i, 'type', true)"
              class="select select-medium"
            >
              <option value="">Appareil...</option>
              <option *ngFor="let type of conditionTypes" [value]="type.value">
                {{ type.label }}
              </option>
            </select>
            <select
              *ngIf="condition.type"
              [(ngModel)]="condition.operator"
              (change)="handleInputChange($event, i, 'operator', true)"
              class="select"
            >
              <option value="">Opérateur...</option>
              <option
                *ngFor="let op of operators[condition.type]"
                [value]="op.value"
              >
                {{ op.label }}
              </option>
            </select>

            <input
              type="text"
              [(ngModel)]="condition.value"
              (input)="handleInputChange($event, i, 'value', true)"
              placeholder="Valeur"
              class="input input-value"
            />
            <button
              *ngIf="newRule.conditions.length > 1"
              mat-icon-button
              color="warn"
              (click)="removeCondition(i)"
            >
              <mat-icon class="mat-icon">close</mat-icon>
            </button>
          </div>
        </div>

        <!-- Actions -->
        <div class="form-group">
          <div class="form-group-header">
            <label class="form-label">Actions (ALORS)</label>
            <button class="add-button" (click)="addAction()">
              + Ajouter une action
            </button>
          </div>
          <div
            *ngFor="let action of newRule.actions; let i = index"
            class="action-row"
          >
            <span *ngIf="i > 0" class="action-and">ET</span>
            <select class="select select-medium">
              <option value="">Type d'action...</option>
              <option value="publish">Publish MQTT Message</option>
              <option value="log">Log Message</option>
              <option value="store_data">Store Data in Memory</option>
              <option value="delay">Delay (blocking)</option>
              <option value="conditional_publish">Conditional Publish</option>
            </select>
            <select
              [(ngModel)]="action.type"
              (change)="handleInputChange($event, i, 'type', false)"
              class="select select-medium"
            >
              <option value="">Type d'action...</option>
              <option *ngFor="let type of actionTypes" [value]="type.value">
                {{ type.label }}
              </option>
            </select>
            <select
              *ngIf="action.type"
              [(ngModel)]="action.action"
              (change)="handleInputChange($event, i, 'action', false)"
              class="select"
            >
              <option value="">Action...</option>
              <option
                *ngFor="let act of actionOptions[action.type]"
                [value]="act.value"
              >
                {{ act.label }}
              </option>
            </select>
            <input
              *ngIf="action.type === 'temperature' || action.type === 'light'"
              type="text"
              [(ngModel)]="action.value"
              (input)="handleInputChange($event, i, 'value', false)"
              placeholder="Valeur"
              class="input input-value"
            />
            <input
              type="text"
              [(ngModel)]="action.target"
              (input)="handleInputChange($event, i, 'target', false)"
              placeholder="Cible (optionnel)"
              class="input"
            />
            <button
              *ngIf="newRule.actions.length > 1"
              mat-icon-button
              color="warn"
              (click)="removeAction(i)"
            >
              <mat-icon class="mat-icon">close</mat-icon>
            </button>
          </div>
        </div>

        <!-- Rule Preview -->
        <div class="preview-group">
          <h3 class="preview-title">Aperçu de la règle</h3>
          <div class="preview-content">
            <pre class="preview-json">{{ getRulePreviewJson() }}</pre>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button mat-button (click)="showCreatePanel = false">Annuler</button>
        <button
          mat-raised-button
          color="primary"
          [disabled]="!newRule.name.trim()"
          (click)="saveNewRule()"
        >
          <mat-icon class="mat-icon">save</mat-icon> Sauvegarder
        </button>
        <button
          mat-button
          class="download-button"
          (click)="downloadRuleJson()"
          [disabled]="!newRule.conditions.length || !newRule.actions.length"
        >
          <mat-icon>download</mat-icon>
          Télécharger JSON
        </button>
      </div>
    </div>
  </div>

  <!-- Edit Rule Panel -->
  <div class="modal" *ngIf="showEditPanel">
    <div class="modal-content">
      <div class="modal-header">
        <h2 class="modal-title">Modifier la règle</h2>
        <button mat-icon-button (click)="showEditPanel = false">
          <mat-icon class="mat-icon">close</mat-icon>
        </button>
      </div>
      <div class="modal-body">
        <!-- Rule Name -->
        <div class="form-group">
          <label class="form-label">Nom de la règle</label>
          <input
            type="text"
            [(ngModel)]="editRule.name"
            class="input"
            placeholder="Ex: Éteindre les lumières quand luminosité élevée"
          />
        </div>

        <!-- Tags -->
        <div class="form-group">
          <label class="form-label">Tags (ex: #france #south)</label>
          <input
            type="text"
            [(ngModel)]="editRule.tags"
            class="input"
            placeholder="#france #south #bedroom"
          />
        </div>

        <!-- Conditions -->
        <div class="form-group">
          <div class="form-group-header">
            <label class="form-label">Conditions (SI)</label>
            <button class="add-button" (click)="addEditCondition()">
              + Ajouter une condition
            </button>
          </div>
          <div
            *ngFor="let condition of editRule.conditions; let i = index"
            class="condition-row"
          >
            <span *ngIf="i > 0" class="condition-and">ET</span>
            <select
              [(ngModel)]="condition.type"
              (change)="handleEditInputChange($event, i, 'type', true)"
              class="select select-medium"
            >
              <option value="">Sélectionner...</option>
              <option *ngFor="let type of conditionTypes" [value]="type.value">
                {{ type.label }}
              </option>
            </select>
            <select
              *ngIf="condition.type"
              [(ngModel)]="condition.operator"
              (change)="handleEditInputChange($event, i, 'operator', true)"
              class="select"
            >
              <option value="">Opérateur...</option>
              <option
                *ngFor="let op of operators[condition.type]"
                [value]="op.value"
              >
                {{ op.label }}
              </option>
            </select>
            <input
              type="text"
              [(ngModel)]="condition.value"
              (input)="handleEditInputChange($event, i, 'value', true)"
              placeholder="Valeur"
              class="input input-value"
            />
            <button
              *ngIf="editRule.conditions.length > 1"
              mat-icon-button
              color="warn"
              (click)="removeEditCondition(i)"
            >
              <mat-icon class="mat-icon">close</mat-icon>
            </button>
          </div>
        </div>

        <!-- Actions -->
        <div class="form-group">
          <div class="form-group-header">
            <label class="form-label">Actions (ALORS)</label>
            <button class="add-button" (click)="addEditAction()">
              + Ajouter une action
            </button>
          </div>
          <div
            *ngFor="let action of editRule.actions; let i = index"
            class="action-row"
          >
            <span *ngIf="i > 0" class="action-and">ET</span>
            <select
              [(ngModel)]="action.type"
              (change)="handleEditInputChange($event, i, 'type', false)"
              class="select select-medium"
            >
              <option value="">Type d'action...</option>
              <option *ngFor="let type of actionTypes" [value]="type.value">
                {{ type.label }}
              </option>
            </select>
            <select
              *ngIf="action.type"
              [(ngModel)]="action.action"
              (change)="handleEditInputChange($event, i, 'action', false)"
              class="select"
            >
              <option value="">Action...</option>
              <option
                *ngFor="let act of actionOptions[action.type]"
                [value]="act.value"
              >
                {{ act.label }}
              </option>
            </select>
            <input
              *ngIf="action.type === 'temperature' || action.type === 'light'"
              type="text"
              [(ngModel)]="action.value"
              (input)="handleEditInputChange($event, i, 'value', false)"
              placeholder="Valeur"
              class="input input-value"
            />
            <input
              type="text"
              [(ngModel)]="action.target"
              (input)="handleEditInputChange($event, i, 'target', false)"
              placeholder="Cible (optionnel)"
              class="input"
            />
            <button
              *ngIf="editRule.actions.length > 1"
              mat-icon-button
              color="warn"
              (click)="removeEditAction(i)"
            >
              <mat-icon class="mat-icon">close</mat-icon>
            </button>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button mat-button (click)="showEditPanel = false">Annuler</button>
        <button
          mat-raised-button
          color="primary"
          [disabled]="!editRule.name.trim()"
          (click)="saveEditRule()"
        >
          <mat-icon class="mat-icon">save</mat-icon> Sauvegarder les
          modifications
        </button>
      </div>
    </div>
  </div>
</div>
