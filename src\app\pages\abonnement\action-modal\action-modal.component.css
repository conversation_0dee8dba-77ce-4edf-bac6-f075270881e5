.modal-overlay {
  position: fixed;
  inset: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 50;
  padding: 1rem;
}

.modal-container {
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  border: 1px solid #e5e7eb;
  max-width: 28rem;
  width: 100%;
  overflow: hidden;
}

.modal-color-bar {
  height: 0.5rem;
  background-color: #10b981;
}

.modal-content {
  padding: 1.5rem;
}

.modal-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: 1rem;
}

.modal-title-container {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.modal-icon {
  padding: 0.5rem;
  background-color: #ffedd5;
  border-radius: 0.5rem;
}

.modal-icon .icon {
  width: 1.5rem;
  height: 1.5rem;
  color: #f97316;
}

.modal-header h3 {
  font-weight: 700;
  color: #111827;
  margin: 0;
  font-family: 'Montserrat', sans-serif;
}

.modal-close-button {
  color: #9ca3af;
  background: none;
  border: none;
  padding: 0.25rem;
  cursor: pointer;
}

.modal-close-button:hover {
  color: #6b7280;
}

.modal-close-button .icon {
  width: 1.5rem;
  height: 1.5rem;
}

.modal-body {
  margin-bottom: 1.5rem;
}

.modal-body p {
  color: #4b5563;
  margin-bottom: 1rem;
  font-family: 'Lato', sans-serif;
}

.subscription-info {
  background-color: #f9fafb;
  padding: 1rem;
  border-radius: 0.5rem;
}

.company-name {
  font-weight: 500;
  color: #111827;
  font-family: 'Montserrat', sans-serif;
}

.contact-email {
  font-size: 0.875rem;
  color: #6b7280;
  font-family: 'Lato', sans-serif;
}

.modal-footer {
  display: flex;
  gap: 0.75rem;
}

.cancel-button {
  flex: 1;
  padding: 0.75rem 1rem;
  border: 1px solid #d1d5db;
  border-radius: 0.5rem;
  color: #374151;
  font-weight: 500;
  background-color: white;
  cursor: pointer;
  font-family: 'Montserrat', sans-serif;
}

.cancel-button:hover {
  background-color: #f9fafb;
}

.confirm-button {
  flex: 1;
  padding: 0.75rem 1rem;
  border: none;
  border-radius: 0.5rem;
  color: white;
  font-weight: 500;
  cursor: pointer;
  font-family: 'Montserrat', sans-serif;
}

.bg-emerald-500 {
  background-color: #10b981;
}

.bg-emerald-500:hover {
  background-color: #059669;
}

.bg-red-500 {
  background-color: #ef4444;
}

.bg-red-500:hover {
  background-color: #dc2626;
}