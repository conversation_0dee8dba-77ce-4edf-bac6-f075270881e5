// src/app/pages/site-details/site-details.component.ts
import { Component, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute } from '@angular/router';
import { Subscription } from 'rxjs';
import { Sites } from '../../core/models/site.model';
import { FloorPlanComponent } from '../../components/floor-plan.component';
import { DeviceStatusPanelComponent } from '../../components/device-status-panel.component';
import { Device, DeviceType, DeviceStatus } from '../../core/models/device.model';
import { CustomMqttService } from '../../core/services/custom-mqtt.service';
import { LocalService } from '@app/core/services/local.service';
import { DeviceManagerService } from '@app/core/services/device-manager.service';
import { Local } from '@app/core/models/local.model';
import { SiteApiService } from '@app/core/services/administrative/site.service';

export interface FloorPlan {
  id: number;
  name: string;
  width: number;
  height: number;
  imageUrl: string;
}
@Component({
  selector: 'app-site-details',
  standalone: true,
  imports: [CommonModule, FloorPlanComponent, DeviceStatusPanelComponent],
  template: `
    <div class="site-details" *ngIf="site; else loadingOrError">
      <header class="site-header">
        <h1>{{ site.name || 'Architecture' }}</h1>
      </header>
      
      <div class="site-content">
        <div class="floor-plan-container">
        <app-floor-plan 
          [site]="site"
          [pairingMode]="pairingMode"
          [devices]="staticDevices"
          [floorPlanImage]="floorPlanImage"
          (devicePositionChanged)="updateDevicePosition($event.deviceId, $event.position)">
        </app-floor-plan>
        </div>
        
        <div class="status-panels">

         <app-device-status-panel 
            title="Autres équipements" 
            [devices]="getOtherDevices()"
            [pairingMode]="pairingMode"
            (deviceStatusChange)="updateDeviceStatus($event.deviceId, $event.status)"
            (togglePermitJoin)="togglePermitJoin($event)">
          </app-device-status-panel>

          <app-device-status-panel 
            title="État lumière" 
            [devices]="getLightDevices()"
            [pairingMode]="pairingMode"
            (deviceStatusChange)="updateDeviceStatus($event.deviceId, $event.status)"
            (togglePermitJoin)="togglePermitJoin($event)">
          </app-device-status-panel>
          
          <app-device-status-panel 
            title="État de climatisation" 
            [devices]="getClimateDevices()"
            [pairingMode]="pairingMode"
            (deviceStatusChange)="updateDeviceStatus($event.deviceId, $event.status)"
            (togglePermitJoin)="togglePermitJoin($event)">
          </app-device-status-panel>
          
         
        </div>
      </div>
    </div>

    <ng-template #loadingOrError>
      <div class="loading" *ngIf="!siteError">Chargement des détails du site...</div>
      <div class="error" *ngIf="siteError">Erreur lors du chargement des détails du site : {{ siteError }}</div>
    </ng-template>
  `,
  styles: [`
    .site-details { padding: 0 0 2rem 0; }
    .site-header { margin-bottom: 2rem; }
    .site-header h1 { font-size: 2rem; margin-bottom: 0.5rem; color: #2c3e50; }
    .site-header p { color: #7f8c8d; margin-bottom: 1.5rem; }
    .site-content { display: flex; flex-direction: column; gap: 2rem; }
    .floor-plan-container { width: 100%; background: white; border-radius: 8px; box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1); overflow: hidden; }
    .status-panels { display: grid; grid-template-columns: repeat(auto-fill, minmax(300px, 1fr)); gap: 1.5rem; }
    .loading, .error { display: flex; justify-content: center; align-items: center; height: 200px; font-size: 1.2rem; color: #7f8c8d; }
    .error { color: #e74c3c; }
    @media (min-width: 1024px) { .site-content { flex-direction: row; } .floor-plan-container { flex: 2; } .status-panels { flex: 1; grid-template-columns: 1fr; } }
  `]
})
export class SiteDetailsComponent implements OnInit, OnDestroy {
  site?: Sites;
  siteId?: number;
  localId?: number;
  siteError: string | null = null;
  readonly subscription = new Subscription();
  floorPlanImage?: string;
  pairingMode = false;

  local?: Local;
  devices: any[] = [];
  private subs = new Subscription();

  staticDevices: Device[] = [
    { id: 'd1', name: 'Lumière du salon', type: DeviceType.LAMP, status: DeviceStatus.RUNNING, position: { x: 100, y: 100 }, isZigbee: true },
    { id: 'd2', name: 'Lumière de la chambre', type: DeviceType.LAMP, status: DeviceStatus.RUNNING, position: { x: 400, y: 150 }, isZigbee: true },
    { id: 'd3', name: 'Lumière de la cuisine', type: DeviceType.LAMP, status: DeviceStatus.PAUSED, position: { x: 200, y: 300 }, isZigbee: false },
    { id: 'd4', name: 'Climatisation de la chambre', type: DeviceType.CLIMATE, status: DeviceStatus.PAUSED, position: { x: 450, y: 200 }, isZigbee: false },
    { id: 'd5', name: 'Climatisation du salon', type: DeviceType.CLIMATE, status: DeviceStatus.RUNNING, position: { x: 300, y: 350 }, isZigbee: true },
    { id: 'd6', name: 'Contrôleur', type: DeviceType.CONTROLLER, status: DeviceStatus.RUNNING, position: { x: 50, y: 450 }, isZigbee: false }
  ];

  constructor(
    readonly route: ActivatedRoute,
    readonly siteService: SiteApiService,
    readonly mqttService: CustomMqttService,
    readonly localService: LocalService,
    private deviceManager: DeviceManagerService

  ) {}

  ngOnInit(): void {
    this.mqttService.connect();
    this.subscription.add(
      this.route.paramMap.subscribe(params => {
        const localId = params.get('id');
        this.localId = localId ? Number(localId) : undefined;

        // Initialize a default site object
        this.site = {
          id: this.localId ?? 0,
          name: 'Local View',
          devices: this.staticDevices,
          floorPlan: {
            id: this.localId?.toString() ?? '0',
            name: 'Plan architecture',
            width: 800,
            height: 600,
            imageUrl: 'assets/plans/salle vide.avif' // Default image
          }
        };

        if (this.localId) {
          this.loadLocalImage(this.localId);
        }
      })
    );

    const localId = this.route.snapshot.paramMap.get('id');
  if (!localId) return;

  this.subs.add(
    this.localService.getLocals().subscribe({
      next: (locals) => {
        this.local = locals.find(l => l.id === +localId);
        if (this.local) {
          this.loadDevices(this.local);
        } else {
          // Handle case where local isn't found
        }
      },
      error: (err) => {
        console.error('Failed to load locals:', err);
      }
    })
  );
  }

  loadDevices(local: Local): void {
    this.subs.add(
      this.deviceManager.subscribeToLocal(local).subscribe(devices => {
        this.devices = devices;
      })
    );
  }

  ngOnDestroy(): void {
    this.subs.unsubscribe();
    this.subscription.unsubscribe();
    this.mqttService.disconnect();
  }

  private loadLocalImage(localId: number): void {
    this.localService.getLocalImageArchitecture(localId).subscribe({
      next: (blob) => {
        const reader = new FileReader();
        reader.onloadend = () => {
          this.floorPlanImage = reader.result as string;
          if (this.site) {
            this.site.floorPlan = {
              id: localId.toString(),
              name: 'Plan architecture',
              width: 800,
              height: 600,
              imageUrl: this.floorPlanImage
            };
          }
        };
        reader.readAsDataURL(blob);
      },
      error: (error) => {
        console.error('Error loading local architecture image:', error);
      }
    });
  }



  updateDevicePosition(deviceId: string, position: { x: number, y: number }): void {
    const device = this.staticDevices.find(d => d.id === deviceId);
    if (device) {
      device.position = position;
      console.log('Position de l\'appareil mise à jour:', { deviceId, position });
    }
  }

  updateDeviceStatus(deviceId: string, status: DeviceStatus): void {
    const device = this.staticDevices.find(d => d.id === deviceId);
    if (device) {
      device.status = status;
      console.log('Statut de l\'appareil mis à jour:', { deviceId, status });
    }
  }

  togglePermitJoin(deviceId: string): void {
    const device = this.staticDevices.find(d => d.id === deviceId);
    if (device && device.type === DeviceType.CONTROLLER) {
      this.pairingMode = !this.pairingMode;
      const message = this.pairingMode
        ? '{"value": true, "time": 254}'
        : '{"value": false}'; // Current payload
      console.log('Toggling Permit Join - New Pairing Mode:', this.pairingMode, 'Message:', message);
      if (!this.pairingMode) {
        // Try a more explicit payload for deactivation
        const deactivationMessage = '{"value": false, "time": 0}'; // Explicitly set time to 0
        setTimeout(() => {
          this.mqttService.publish('zigbee2mqtt/bridge/request/permit_join', deactivationMessage);
          console.log('Deactivation message sent with delay:', deactivationMessage);
        }, 1000);
      } else {
        this.mqttService.publish('zigbee2mqtt/bridge/request/permit_join', message);
        console.log('Activation message sent');
      }
      device.status = this.pairingMode ? DeviceStatus.PAIRING : DeviceStatus.RUNNING;
      console.log(`Appairage ${this.pairingMode ? 'activé' : 'désactivé'} pour ${device.name} via MQTT`);
    }
  }

  getLightDevices(): Device[] {
    return this.staticDevices.filter(d => d.type === DeviceType.LAMP);
  }

  getClimateDevices(): Device[] {
    return this.staticDevices.filter(d => d.type === DeviceType.CLIMATE);
  }

  getOtherDevices(): Device[] {
    return this.staticDevices.filter(d => d.type !== DeviceType.LAMP && d.type !== DeviceType.CLIMATE);
  }
}