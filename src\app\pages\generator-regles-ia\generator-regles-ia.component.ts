import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatDialogModule, MatDialog } from '@angular/material/dialog';
import { CdkDragDrop, moveItemInArray, DragDropModule } from '@angular/cdk/drag-drop';
import { DeleteConfirmDialogComponent } from '../app-delete-confirm-dialog/app-delete-confirm-dialog.component';

interface Condition {
  type: string;
  operator: string;
  value: string;
}

interface Action {
  type: string;
  action: string;
  value: string;
  target: string;
}

interface Rule {
  id: number;
  name: string;
  priority: number;
  status: string;
  tags: string[];
  conditions: Condition[];
  actions: Action[];
  tagStatus: { [key: string]: string };
}

interface RuleForm extends Omit<Rule, 'tags'> {
  tags: string;
}

@Component({
  selector: 'app-generator-regles-ia',
  templateUrl: './generator-regles-ia.component.html',
  styleUrls: ['./generator-regles-ia.component.css'],
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatIconModule,
    MatButtonModule,
    MatDialogModule,
    DragDropModule
  ]
})
export class GeneratorReglesIaComponent implements OnInit {
  rules: Rule[] = [
    {
      id: 1,
      name: 'Allumer les lumières au coucher',
      priority: 1,
      status: 'active',
      tags: ['#france', '#south'],
      conditions: [{ type: 'time', operator: 'after', value: '22:00' }],
      actions: [{ type: 'light', action: 'turn_on', target: 'bedroom_lights', value: '' }],
      tagStatus: { '#france': 'active', '#bedroom': 'active' }
    },
    {
      id: 2,
      name: 'Éteindre le chauffage si fenêtre ouverte',
      priority: 2,
      status: 'active',
      tags: ['#maroc', '#france'],
      conditions: [
        { type: 'window_sensor', operator: 'equals', value: 'open' },
        { type: 'temperature', operator: 'greater', value: '18' }
      ],
      actions: [
        { type: 'temperature', action: 'set', target: 'livingroom_heater', value: '16' }
      ],
      tagStatus: { '#energy': 'active', '#livingroom': 'active' }
    },
    {
      id: 3,
      name: 'Ouvrir les volets au lever',
      priority: 3,
      status: 'active',
      tags: ['#france', '#usa'],
      conditions: [
        { type: 'time', operator: 'after', value: '07:00' },
        { type: 'luminosity', operator: 'greater', value: '50' }
      ],
      actions: [
        { type: 'blind', action: 'open', target: 'livingroom_blinds', value: '' }
      ],
      tagStatus: { '#france': 'active', '#usa': 'active' }
    },
    {
      id: 4,
      name: 'Alerte intrusion porte entrée',
      priority: 1,
      status: 'active',
      tags: ['#france', '#maroc'],
      conditions: [
        { type: 'door_sensor', operator: 'equals', value: 'open' },
        { type: 'time', operator: 'between', value: '00:00-06:00' }
      ],
      actions: [
        { type: 'security', action: 'activate', target: 'alarm_system', value: '' },
        { type: 'notification', action: 'send', target: 'owner_phone', value: 'Intrusion détectée!' }
      ],
      tagStatus: { '#france': 'active', '#maroc': 'active' }
    },
    {
      id: 5,
      name: 'Mode économie énergie nuit',
      priority: 4,
      status: 'inactive',
      tags: ['#france', '#south'],
      conditions: [
        { type: 'time', operator: 'between', value: '23:00-06:00' },
        { type: 'presence', operator: 'not_equals', value: 'detected' }
      ],
      actions: [
        { type: 'light', action: 'turn_off', target: 'all_lights', value: '' },
        { type: 'temperature', action: 'decrease', target: 'all_thermostats', value: '2' }
      ],
      tagStatus: { '#france': 'active', '#south': 'inactive' }
    }
  ];

  isDragMode = false;
  showCreatePanel = false;
  showEditPanel = false;
  editingRule: Rule | null = null;

  newRule: RuleForm = {
    id: 0,
    name: '',
    priority: 0,
    status: 'active',
    tags: '',
    conditions: [{ type: '', operator: '', value: '' }],
    actions: [{ type: '', action: '', value: '', target: '' }],
    tagStatus: {}
  };

  editRule: RuleForm = {
    id: 0,
    name: '',
    priority: 0,
    status: 'active',
    tags: '',
    conditions: [{ type: '', operator: '', value: '' }],
    actions: [{ type: '', action: '', value: '', target: '' }],
    tagStatus: {}
  };

  conditionTypes = [
    { value: 'door_sensor', label: 'Capteur de porte' },
    { value: 'window_sensor', label: 'Capteur de fenêtre' },
    { value: 'blind_sensor', label: 'Volet roulant' },
    { value: 'luminosity', label: 'Luminosité' },
    { value: 'temperature', label: 'Température' },
    { value: 'presence', label: 'Présence' },
    { value: 'time', label: 'Heure' },
    { value: 'light_status', label: 'État des lumières' }
  ];

  operators: { [key: string]: { value: string; label: string }[] } = {
    door_sensor: [
      { value: 'equals', label: '=' },
      { value: 'not_equals', label: '≠' }
    ],
    luminosity: [
      { value: 'greater', label: '>' },
      { value: 'less', label: '<' },
      { value: 'equals', label: '=' }
    ],
    temperature: [
      { value: 'greater', label: '>' },
      { value: 'less', label: '<' },
      { value: 'equals', label: '=' }
    ],
    presence: [
      { value: 'equals', label: '=' },
      { value: 'not_equals', label: '≠' }
    ],
    time: [
      { value: 'after', label: 'Après' },
      { value: 'before', label: 'Avant' },
      { value: 'between', label: 'Entre' }
    ],
    light_status: [
      { value: 'equals', label: '=' },
      { value: 'not_equals', label: '≠' }
    ]
  };

  actionTypes = [
    { value: 'light', label: 'Lumière' },
    { value: 'temperature', label: 'Température' },
    { value: 'blind', label: 'Volet' },
    { value: 'security', label: 'Sécurité' },
    { value: 'notification', label: 'Notification' }
  ];

  actionOptions: { [key: string]: { value: string; label: string }[] } = {
    light: [
      { value: 'turn_on', label: 'Allumer' },
      { value: 'turn_off', label: 'Éteindre' },
      { value: 'dim', label: 'Tamiser' }
    ],
    temperature: [
      { value: 'set', label: 'Régler à' },
      { value: 'increase', label: 'Augmenter de' },
      { value: 'decrease', label: 'Diminuer de' }
    ],
    blind: [
      { value: 'open', label: 'Ouvrir' },
      { value: 'close', label: 'Fermer' },
      { value: 'partial', label: 'Partiellement' }
    ],
    security: [
      { value: 'activate', label: 'Activer' },
      { value: 'deactivate', label: 'Désactiver' }
    ],
    notification: [
      { value: 'send', label: 'Envoyer' }
    ]
  };

  constructor(readonly dialog: MatDialog) {}

  ngOnInit(): void {}

  removeEditCondition(index: number) {
    if (this.editRule.conditions.length > 1) {
      this.editRule.conditions = this.editRule.conditions.filter((_, i) => i !== index);
    }
  }

  removeEditAction(index: number) {
    if (this.editRule.actions.length > 1) {
      this.editRule.actions = this.editRule.actions.filter((_, i) => i !== index);
    }
  }

  getRulePreviewJson(): string {
    const ruleId = this.newRule.id || Date.now();
    const conditions = this.newRule.conditions.map((condition, index) => ({
      id: index + 1,
      deviceId: null,
      propertyName: condition.type || '',
      operator: condition.operator || '',
      value: condition.value || ''
    }));
    const actions = this.newRule.actions.map((action, index) => ({
      id: index + 1,
      deviceId: action.target || null,
      actionType: action.type || '',
      actionValue: action.value ? `${action.action} ${action.value}` : action.action || ''
    }));

    const ruleJson = {
      IF: {
        id: ruleId,
        logicalOperator: 'AND',
        conditions,
        childGroups: []
      },
      THEN: actions
    };

    return JSON.stringify(ruleJson, null, 2)
      .replace(/"IF"/, 'IF')
      .replace(/"THEN"/, 'THEN');
  }

  saveNewRule(): void {
    const tagsArray: string[] = this.newRule.tags
      .split(/\s+/)
      .filter(tag => tag.startsWith('#') && tag.length > 1);
  
    const tagStatus: { [key: string]: string } = {};
    tagsArray.forEach(tag => tagStatus[tag] = 'active');
  
    const newId = this.rules.length ? Math.max(...this.rules.map(r => r.id)) + 1 : 1;
  
    const rule: Rule = {
      id: newId,
      name: this.newRule.name,
      priority: this.newRule.priority,
      status: this.newRule.status,
      tags: tagsArray,
      conditions: [...this.newRule.conditions],
      actions: [...this.newRule.actions],
      tagStatus
    };
  
    this.rules.push(rule);
    this.showCreatePanel = false;
    this.resetNewRule();
  }
  

  saveEditRule(): void {
    if (!this.editRule) return;
  
    const tagsArray: string[] = this.editRule.tags
      .split(/\s+/)
      .filter(tag => tag.startsWith('#') && tag.length > 1);
  
    const tagStatus: { [key: string]: string } = {};
    tagsArray.forEach(tag => tagStatus[tag] = 'active');
  
    const updatedRule: Rule = {
      id: this.editRule.id,
      name: this.editRule.name,
      priority: this.editRule.priority,
      status: this.editRule.status,
      tags: tagsArray,
      conditions: [...this.editRule.conditions],
      actions: [...this.editRule.actions],
      tagStatus
    };
  
    const index = this.rules.findIndex(r => r.id === updatedRule.id);
    if (index !== -1) {
      this.rules[index] = updatedRule;
    }
  
    this.showEditPanel = false;
    this.editingRule = null;
  }
  

  confirmDeleteRule(ruleId: number) {
    const dialogRef = this.dialog.open(DeleteConfirmDialogComponent, {
      data: { ruleName: this.rules.find(r => r.id === ruleId)?.name }
    });
  }

  toggleDragMode(): void {
    this.isDragMode = !this.isDragMode;
  }

  drop(event: CdkDragDrop<Rule[]>): void {
    moveItemInArray(this.rules, event.previousIndex, event.currentIndex);
    this.rules.forEach((rule, index) => rule.priority = index + 1); // Update priorities
  }

  toggleTagStatus(ruleId: number, tag: string): void {
    const rule = this.rules.find(r => r.id === ruleId);
    if (rule && rule.status !== 'inactive') {
      rule.tagStatus[tag] = rule.tagStatus[tag] === 'active' ? 'inactive' : 'active';
    }
  }

  toggleRuleStatus(ruleId: number): void {
    const rule = this.rules.find(r => r.id === ruleId);
    if (rule) {
      rule.status = rule.status === 'active' ? 'inactive' : 'active';
    }
  }

  handleInputChange(event: Event, index: number, field: keyof Condition | keyof Action, isCondition: boolean = true): void {
    const target = event.target as HTMLInputElement | HTMLSelectElement;
    if (target) {
      const value = target.value;
      if (isCondition) {
        const condition = this.newRule.conditions[index];
        if (condition) {
          condition[field as keyof Condition] = value;
          if (field === 'type') {
            condition.operator = '';
            condition.value = '';
          }
        }
      } else {
        const action = this.newRule.actions[index];
        if (action) {
          action[field as keyof Action] = value;
          if (field === 'type') {
            action.action = '';
            action.value = '';
            action.target = '';
          }
        }
      }
    }
  }

  handleEditInputChange(event: Event, index: number, field: keyof Condition | keyof Action, isCondition: boolean = true): void {
    const target = event.target as HTMLInputElement | HTMLSelectElement;
    if (target) {
      const value = target.value;
      if (isCondition) {
        const condition = this.editRule.conditions[index];
        if (condition) {
          condition[field as keyof Condition] = value;
          if (field === 'type') {
            condition.operator = '';
            condition.value = '';
          }
        }
      } else {
        const action = this.editRule.actions[index];
        if (action) {
          action[field as keyof Action] = value;
          if (field === 'type') {
            action.action = '';
            action.value = '';
            action.target = '';
          }
        }
      }
    }
  }

  openEditPanel(rule: Rule): void {
    this.editingRule = rule;
    this.editRule = {
      ...rule,
      tags: rule.tags.join(' '),
      conditions: [...rule.conditions],
      actions: [...rule.actions],
    };
    this.showEditPanel = true;
  }

  addCondition(): void {
    this.newRule.conditions.push({ type: '', operator: '', value: '' });
  }

  removeCondition(index: number): void {
    if (this.newRule.conditions.length > 1) {
      this.newRule.conditions = this.newRule.conditions.filter((_, i) => i !== index);
    }
  }

  addAction(): void {
    this.newRule.actions.push({ type: '', action: '', value: '', target: '' });
  }

  removeAction(index: number): void {
    if (this.newRule.actions.length > 1) {
      this.newRule.actions = this.newRule.actions.filter((_, i) => i !== index);
    }
  }

  addEditCondition(): void {
    this.editRule.conditions.push({ type: '', operator: '', value: '' });
  }

  addEditAction(): void {
    this.editRule.actions.push({ type: '', action: '', value: '', target: '' });
  }

  resetNewRule(): void {
    this.newRule = {
      id: 0,
      name: '',
      priority: 0,
      status: 'active',
      tags: '',
      conditions: [{ type: '', operator: '', value: '' }],
      actions: [{ type: '', action: '', value: '', target: '' }],
      tagStatus: {}
    };
  }

  // Add this method to your component
downloadRuleJson(): void {
  const jsonContent = this.getRulePreviewJson();
  const blob = new Blob([jsonContent], { type: 'text/plain;charset=utf-8' });
  const url = window.URL.createObjectURL(blob);
  const link = document.createElement('a');
  
  // Create filename using rule name or timestamp
  const filename = this.newRule.name 
    ? `rule_${this.newRule.name.toLowerCase().replace(/\s+/g, '_')}.txt`
    : `rule_${new Date().getTime()}.txt`;
  
  link.href = url;
  link.download = filename;
  link.click();
  
  window.URL.revokeObjectURL(url);
}
}