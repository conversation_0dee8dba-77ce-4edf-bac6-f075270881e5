<div class="organisation-details-container">
    <div class="breadcrumb-nav" style="display: flex !important; align-items: center !important; gap: 8px;">
    <button class="back-button" style="display: flex !important; align-items: center !important; border: none; background: none; cursor: pointer;" (click)="goBack()">
      <i class="material-icons">arrow_back</i>
    </button>
    <span class="breadcrumb-text">Détails de site: {{ site?.nom }}</span>
</div>

<div class="locals-container">
  <!-- <div class="breadcrumb-nav">
    <button
      class="back-button"
      (click)="goBack()"
    >
      <i class="material-icons">arrow_back</i>
    </button>
    <span class="breadcrumb-text" style="margin-bottom: 10px !important;" *ngIf="site"
      >Détails de {{ site.nom }}</span
    >
  </div> -->
  

  <div class="stats-section">
    <div class="stats-grid">
      <div class="stat-card">
        <div class="stat-icon room">
          <i class="material-icons">meeting_room</i>
        </div>
        <div class="stat-info">
          <div class="stat-value">{{ locals.length }}</div>
          <div class="stat-label">Total Locaux</div>
        </div>
      </div>

      <div class="stat-card">
        <div class="stat-icon energy">
          <i class="material-icons">bolt</i>
        </div>
        <div class="stat-info">
          <div class="stat-value">
            {{ getTotalConsumption() | number : "1.0-0" }} kWh
          </div>
          <div class="stat-label">Consommation Totale</div>
        </div>
      </div>

      <div class="stat-card">
        <div class="stat-icon capacity">
          <i class="material-icons">groups</i>
        </div>
        <div class="stat-info">
          <div class="stat-value">{{ getTotalCapacity() }}</div>
          <div class="stat-label">Capacité Totale</div>
        </div>
      </div>

      <div class="stat-card">
        <div class="stat-icon area">
          <i class="material-icons">square_foot</i>
        </div>
        <div class="stat-info">
          <div class="stat-value">
            {{ getTotalSurface() | number : "1.0-0" }} m²
          </div>
          <div class="stat-label">Surface Totale</div>
        </div>
      </div>
    </div>
  </div>
  <!-- Header Section -->
  <div class="header-section">
    <div class="page-title">
      <h1 class="title">
        <i class="material-icons title-icon">meeting_room</i> Gestion des Locaux
      </h1>
      <p class="subtitle">Visualisez et gérez vos locaux avec facilité</p>
    </div>

    <div class="actions">
      <button
        class="create-button"
        (click)="showAddLocalForm()"
        *ngIf="!showCreateForm"
      >
        <i class="material-icons action-icon">add</i> Ajouter Local
      </button>
      <button class="view-toggle" (click)="toggleViewMode()">
        <i class="material-icons action-icon">{{
          viewMode === "cards" ? "view_list" : "grid_view"
        }}</i>
        {{ viewMode === "cards" ? "Vue Tableau" : "Vue Cartes" }}
      </button>
    </div>
  </div>

  <!-- Create Form -->
  <div class="create-form-card" *ngIf="showCreateForm" id="createLocalForm">
    <h2 class="form-title">Nouveau Local</h2>
    <form [formGroup]="createLocalForm" (ngSubmit)="submitCreateForm()">
      <div class="form-grid">
        <div class="form-group">
          <label for="siteId">Site ID*</label>
          <input
            id="siteId"
            type="number"
            min="1"
            formControlName="siteId"
            required
            class="form-control"
          />
          <div
            class="error-message"
            *ngIf="
              createLocalForm.get('siteId')?.invalid &&
              createLocalForm.get('siteId')?.touched
            "
          >
            Site ID est requis et doit être supérieur à 0
          </div>
        </div>

        <div class="form-group">
          <label for="type">Type de local*</label>
          <input
            id="type"
            list="localTypesList"
            formControlName="type"
            required
            class="form-control"
            placeholder="Sélectionner ou saisir un type"
          />
          <datalist id="localTypesList">
            <option *ngFor="let type of localTypes" [value]="type.value">
              {{ type.label }}
            </option>
          </datalist>
          <div
            class="error-message"
            *ngIf="
              createLocalForm.get('type')?.invalid &&
              createLocalForm.get('type')?.touched
            "
          >
            Type de local est requis
          </div>
        </div>

        <div class="form-group">
          <label for="surface">Surface (m²)*</label>
          <input
            id="surface"
            type="number"
            min="0"
            step="0.1"
            formControlName="surface"
            required
            class="form-control"
          />
          <div
            class="error-message"
            *ngIf="
              createLocalForm.get('surface')?.invalid &&
              createLocalForm.get('surface')?.touched
            "
          >
            Surface est requise et doit être positive
          </div>
        </div>

        <div class="form-group">
          <label for="hauteurSousPlafond">Hauteur sous plafond (m)*</label>
          <input
            id="hauteurSousPlafond"
            type="number"
            min="0"
            step="0.1"
            formControlName="hauteurSousPlafond"
            required
            class="form-control"
          />
          <div
            class="error-message"
            *ngIf="
              createLocalForm.get('hauteurSousPlafond')?.invalid &&
              createLocalForm.get('hauteurSousPlafond')?.touched
            "
          >
            Hauteur sous plafond est requise et doit être positive
          </div>
        </div>

        <div class="form-group">
          <label for="capacitePersonnes">Capacité (personnes)</label>
          <input
            id="capacitePersonnes"
            type="number"
            min="0"
            formControlName="capacitePersonnes"
            class="form-control"
          />
          <div
            class="error-message"
            *ngIf="
              createLocalForm.get('capacitePersonnes')?.invalid &&
              createLocalForm.get('capacitePersonnes')?.touched
            "
          >
            La capacité doit être positive
          </div>
        </div>

        <div class="form-group">
          <label for="etage">Étage*</label>
          <input
            id="etage"
            type="number"
            formControlName="etage"
            class="form-control"
            required
          />
          <div
            class="error-message"
            *ngIf="
              createLocalForm.get('etage')?.invalid &&
              createLocalForm.get('etage')?.touched
            "
          >
            Étage est requis
          </div>
        </div>

        <div class="form-group">
          <label for="temperatureCible">Température cible (°C)*</label>
          <input
            id="temperatureCible"
            type="number"
            min="0"
            max="50"
            step="0.5"
            formControlName="temperatureCible"
            required
            class="form-control"
          />
          <div
            class="error-message"
            *ngIf="
              createLocalForm.get('temperatureCible')?.invalid &&
              createLocalForm.get('temperatureCible')?.touched
            "
          >
            Température cible est requise
          </div>
        </div>

        <div class="form-group">
          <label for="consommationElectriqueMensuelle"
            >Consommation électrique mensuelle (kWh)</label
          >
          <input
            id="consommationElectriqueMensuelle"
            type="number"
            min="0"
            step="0.1"
            formControlName="consommationElectriqueMensuelle"
            class="form-control"
          />
          <div
            class="error-message"
            *ngIf="
              createLocalForm.get('consommationElectriqueMensuelle')?.invalid &&
              createLocalForm.get('consommationElectriqueMensuelle')?.touched
            "
          >
            La consommation doit être positive
          </div>
        </div>

        <div class="form-group">
          <label for="dateDerniereMaintenance"
            >Date dernière maintenance*</label
          >
          <input
            id="dateDerniereMaintenance"
            type="date"
            formControlName="dateDerniereMaintenance"
            required
            class="form-control"
          />
          <div
            class="error-message"
            *ngIf="
              createLocalForm.get('dateDerniereMaintenance')?.invalid &&
              createLocalForm.get('dateDerniereMaintenance')?.touched
            "
          >
            Date de dernière maintenance est requise
          </div>
        </div>

        <div class="form-group">
          <label for="description">Description</label>
          <textarea
            id="description"
            formControlName="description"
            class="form-control"
            rows="3"
            placeholder="Description optionnelle du local"
          ></textarea>
        </div>
      </div>

      <!-- Add these fields inside the form-grid div, before the form-actions -->
      <div class="form-group">
        <label for="architecture2D">Plan d'architecture 2D</label>
        <div class="file-upload">
          <input
            type="file"
            id="architecture2D"
            (change)="onArchitecture2DSelected($event)"
            accept="image/*"
            class="form-control"
          />
          <div class="preview" *ngIf="architecture2DPreview">
            <img [src]="architecture2DPreview" alt="Architecture 2D Preview" />
          </div>
        </div>
      </div>

      <div class="form-group">
        <label for="localeImage">Photo du local</label>
        <div class="file-upload">
          <input
            type="file"
            id="localeImage"
            (change)="onLocaleImageSelected($event)"
            accept="image/*"
            class="form-control"
          />
          <div class="preview" *ngIf="localeImagePreview">
            <img [src]="localeImagePreview" alt="Local Preview" />
          </div>
        </div>
      </div>

      <div class="form-group full-width">
        <label for="tags">Tags</label>
        <div class="tags-input">
          <input
            type="text"
            id="newTag"
            [(ngModel)]="newTag"
            [ngModelOptions]="{ standalone: true }"
            (keyup.enter)="addTag()"
            placeholder="Ajouter un tag et appuyer sur Entrée"
          />
          <div class="tags-list">
            <span class="tag" *ngFor="let tag of tags">
              {{ tag }}
              <i
                onkeypress=""
                class="material-icons remove-tag"
                (click)="removeTag(tag)"
                >close</i
              >
            </span>
          </div>
        </div>
      </div>

      <div class="form-actions">
        <button type="button" class="btn-cancel" (click)="hideAddLocalForm()">
          Annuler
        </button>
        <button
          type="submit"
          class="btn-submit"
          [disabled]="createLocalForm.invalid"
        >
          Créer
        </button>
      </div>
    </form>
  </div>

  <!-- Search Bar -->
  <div class="search-bar">
    <div class="search-field">
      <i class="material-icons search-icon">search</i>
      <input
        type="text"
        [(ngModel)]="searchTerm"
        (keyup)="filterLocals()"
        placeholder="Rechercher un local par type"
      />
      <button
        *ngIf="searchTerm"
        (click)="searchTerm = ''; filterLocals()"
        class="clear-button"
      >
        <i class="material-icons">close</i>
      </button>
    </div>
  </div>

  <!-- Loading Spinner -->
  <div class="loading-container" *ngIf="isLoading">
    <div class="spinner"></div>
    <p>Chargement des locaux...</p>
  </div>

  <!-- No Results Message -->
  <div class="no-data" *ngIf="!isLoading && filteredLocals.length === 0">
    <i class="material-icons">warning</i>
    <p>Aucun local trouvé</p>
  </div>

  <!-- Cards View -->
  <div
    class="cards-view-container"
    *ngIf="viewMode === 'cards' && !isLoading && filteredLocals.length > 0"
  >
    <div class="cards-container">
      <app-card-local
        *ngFor="let local of paginatedLocals"
        (viewDetails)="viewLocalDetails($event)"
        (editLocal)="editLocal($event)"
        (deleteLocal)="deleteLocal($event)"
      >
      </app-card-local>
    </div>

    <!-- Pagination Controls -->
    <div class="pagination-controls">
      <button
        class="pagination-button"
        [disabled]="currentPage === 1"
        (click)="previousPage()"
      >
        <i class="material-icons">chevron_left</i>
      </button>

      <div class="page-numbers">
        <button
          *ngFor="let page of getPageNumbers()"
          class="page-number-button"
          [class.active]="page === currentPage"
          [class.separator]="page === -1"
          [disabled]="page === -1"
          (click)="page !== -1 && onPageChange(page)"
        >
          {{ page === -1 ? "..." : page }}
        </button>
      </div>

      <button
        class="pagination-button"
        [disabled]="currentPage === totalPages"
        (click)="nextPage()"
      >
        <i class="material-icons">chevron_right</i>
      </button>
    </div>
  </div>

  <!-- Table View -->
  <div
    class="table-container"
    *ngIf="viewMode === 'table' && !isLoading && filteredLocals.length > 0"
  >
    <table class="data-table">
      <thead>
        <tr>
          <th>Type</th>
          <th>Étage</th>
          <th>Surface</th>
          <th>Hauteur</th>
          <th>Capacité</th>
          <th>Température</th>
          <th>Dernière maintenance</th>
          <th>Actions</th>
        </tr>
      </thead>
      <tbody>
      <tr *ngFor="let local of paginatedLocals">
          <td>{{local.type}}</td>
          <td>{{local.etage}}</td>
          <td>{{local.surface}} m²</td>
          <td>{{local.hauteurSousPlafond}} m</td>
          <td>{{local.capacitePersonnes}}</td>
          <td>{{local.temperatureCible}}°C</td>
          <td>{{local.dateDerniereMaintenance | date:'shortDate'}}</td>
          <td class="actions-cell">
            <button
              class="action-button view"
              (click)="viewLocalDetails(local.id)"
              title="Voir détails"
            >
              <i class="material-icons">visibility</i>
            </button>
            <button
              class="action-button edit"
              (click)="editLocal(local.id)"
              title="Modifier"
            >
              <i class="material-icons">edit</i>
            </button>
            <button
              class="action-button delete"
              (click)="deleteLocal(local.id)"
              title="Supprimer"
            >
              <i class="material-icons">delete</i>
            </button>
          </td>
        </tr>
      </tbody>
    </table>
    <div class="pagination-controls">
      <button
        class="pagination-button"
        [disabled]="currentPage === 1"
        (click)="previousPage()"
      >
        <i class="material-icons">chevron_left</i>
      </button>

      <div class="page-numbers">
        <button
          *ngFor="let page of getPageNumbers()"
          class="page-number-button"
          [class.active]="page === currentPage"
          [class.separator]="page === -1"
          [disabled]="page === -1"
          (click)="page !== -1 && onPageChange(page)"
        >
          {{ page === -1 ? "..." : page }}
        </button>
      </div>

      <button
        class="pagination-button"
        [disabled]="currentPage === totalPages"
        (click)="nextPage()"
      >
        <i class="material-icons">chevron_right</i>
      </button>
    </div>
  </div>

  <!-- Edit Form Component -->
  <app-edit-local
    *ngIf="showEditForm && selectedLocal"
    [local]="selectedLocal"
    (saveChanges)="onSaveChanges($event)"
    (cancel)="onCancelEdit()"
    id="editLocalForm"
  >
  </app-edit-local>
</div>
