<!-- src/app/shared/components/sidebar/sidebar.component.html -->
<div class="sidebar-wrapper">
  <div class="sidebar" [ngClass]="{ 'collapsed': isCollapsed }">
        <div class="sidebar-header">
      <div class="logo-container">
        <div class="logo-wrapper">
          <img src="assets/images/logoIOT.png" alt="Logo" class="logo" />
        </div>
        <button 
          class="toggle-button" 
          (click)="toggleSidebar()" 
          [@toggleIconAnimation]="isCollapsed ? 'collapsed' : 'expanded'"
        >
          <mat-icon>chevron_left</mat-icon>
        </button>
      </div>
    </div>
  <div class="sidebar-content">
    <a class="sidebar-item" routerLink="/organisations" routerLinkActive="active">
      <mat-icon>dashboard</mat-icon>
      <span *ngIf="!isCollapsed">Accueil</span>
    </a>
        <!-- Add Paramétrage Menu -->
  <div onkeydown="" class="sidebar-item parent-menu" (click)="toggleParametrage()">
    <mat-icon>settings</mat-icon>
    <span *ngIf="!isCollapsed">Paramétrage</span>
    <mat-icon class="expand-icon" *ngIf="!isCollapsed">
      {{showParametrage ? 'expand_less' : 'expand_more'}}
    </mat-icon>
  </div>

  <!-- Paramétrage Submenus -->
  <div class="submenu" *ngIf="showParametrage && !isCollapsed">
    <!-- Organisation -->
    <div onkeydown=""  class="sidebar-item submenu-item" (click)="toggleOrganisation()">
      <mat-icon>business</mat-icon>
      <span>Organisation</span>
      <mat-icon class="expand-icon">
        {{showOrganisation ? 'expand_less' : 'expand_more'}}
      </mat-icon>
    </div>
    
    <div class="nested-submenu" *ngIf="showOrganisation">
  <a class="sidebar-item nested-item" 
     [routerLink]="['/organisation-management']" 
     routerLinkActive="active"
     (click)="$event.stopPropagation()">
    <mat-icon>format_list_bulleted</mat-icon>
    <span>Liste</span>
  </a>
        <a class="sidebar-item nested-item" 
     [routerLink]="['/organisation-management']" 
     [queryParams]="{action: 'create'}"
     routerLinkActive="active"
     (click)="$event.stopPropagation()">
    <mat-icon>add</mat-icon>
    <span>Ajouter</span>
  </a>
    </div>

    <!-- Sites -->
    <div onkeydown=""  class="sidebar-item submenu-item" (click)="toggleSites()">
      <mat-icon>location_on</mat-icon>
      <span>Sites</span>
      <mat-icon class="expand-icon">
        {{showSites ? 'expand_less' : 'expand_more'}}
      </mat-icon>
    </div>

    <div class="nested-submenu" *ngIf="showSites">
  <a class="sidebar-item nested-item" 
     routerLink="/site-management" 
     routerLinkActive="active">
    <mat-icon>format_list_bulleted</mat-icon>
    <span>Liste</span>
  </a>
  <a class="sidebar-item nested-item" 
     [routerLink]="['/site-management']" 
     [queryParams]="{action: 'create'}"
     routerLinkActive="active"
     (click)="$event.stopPropagation()">
    <mat-icon>add</mat-icon>
    <span>Ajouter</span>
  </a>
    </div>

    <!-- Locaux -->
    <div onkeydown=""  class="sidebar-item submenu-item" (click)="toggleLocaux()">
      <mat-icon>meeting_room</mat-icon>
      <span>Locaux</span>
      <mat-icon class="expand-icon">
        {{showLocaux ? 'expand_less' : 'expand_more'}}
      </mat-icon>
    </div>

    <div class="nested-submenu" *ngIf="showLocaux">
  <a class="sidebar-item nested-item" 
     routerLink="/local-management" 
     routerLinkActive="active">
    <mat-icon>format_list_bulleted</mat-icon>
    <span>Liste</span>
  </a>
  <a class="sidebar-item nested-item" 
     [routerLink]="['/local-management']" 
     [queryParams]="{action: 'create'}"
     routerLinkActive="active"
     (click)="$event.stopPropagation()">
    <mat-icon>add</mat-icon>
    <span>Ajouter</span>
  </a>
    </div>
  </div>

        <a class="sidebar-item" routerLink="/energy-report" routerLinkActive="active">
      <mat-icon>assessment</mat-icon>
      <span *ngIf="!isCollapsed">Rapports Énergétiques</span>
    </a>
    <a class="sidebar-item" routerLink="/devices" routerLinkActive="active">
      <mat-icon>network_check</mat-icon>
      <span *ngIf="!isCollapsed">Network Monitoring</span>
    </a>
     <a class="sidebar-item" routerLink="/subscriptions" routerLinkActive="active">
      <mat-icon>card_membership</mat-icon>
      <span *ngIf="!isCollapsed">Abonnement</span>
    </a>

    <a class="sidebar-item" routerLink="/iot" routerLinkActive="active">
      <mat-icon>devices</mat-icon>
      <span *ngIf="!isCollapsed">IoT</span>
    </a>
    <a class="sidebar-item" routerLink="/projects" routerLinkActive="active">
      <mat-icon>folder</mat-icon>
      <span *ngIf="!isCollapsed">Projects</span>
    </a>
    <a class="sidebar-item" routerLink="/generator-regles-ia" routerLinkActive="active">
      <mat-icon>rule</mat-icon>
      <span *ngIf="!isCollapsed">Gestion des Règles
      </span>
    </a>
    <a class="sidebar-item" routerLink="/reglesIA" routerLinkActive="active">
      <mat-icon>rule</mat-icon>
      <span *ngIf="!isCollapsed">Règles en IA
      </span>
    </a>
    <a class="sidebar-item" routerLink="/accounts" routerLinkActive="active" *ngIf="isAdmin || isSuperAdmin">
      <mat-icon>people</mat-icon>
      <span *ngIf="!isCollapsed">Accounts</span>
    </a>
  </div>
</div>