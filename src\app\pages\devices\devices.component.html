<div class="device-control-container">
  <mat-card class="header-card">
      <mat-card-header>
        <div mat-card-avatar class="header-avatar">
          <mat-icon>hub</mat-icon>
        </div>
        <mat-card-title>Contrôle des Appareils</mat-card-title>
      </mat-card-header>
  </mat-card>
  <div class="stats-grid">
      <mat-card class="stat-card">
        <mat-card-content>
          <div class="stat-content">
            <mat-icon class="stat-icon total">sensors</mat-icon>
            <div class="stat-info">
              <div class="stat-number">{{ deviceStats.total }}</div>
              <div class="stat-label">Appareils Total</div>
            </div>
          </div>
        </mat-card-content>
      </mat-card>
      <mat-card class="stat-card">
        <mat-card-content>
          <div class="stat-content">
            <mat-icon class="stat-icon online">wifi</mat-icon>
            <div class="stat-info">
              <div class="stat-number">{{ deviceStats.online }}</div>
              <div class="stat-label">En Ligne</div>
            </div>
          </div>
        </mat-card-content>
      </mat-card>

      <mat-card class="stat-card">
        <mat-card-content>
          <div class="stat-content">
            <mat-icon class="stat-icon lights">lightbulb</mat-icon>
            <div class="stat-info">
              <div class="stat-number">{{ deviceStats.lights }}</div>
              <div class="stat-label">Lumières</div>
            </div>
          </div>
        </mat-card-content>
      </mat-card>

      <mat-card class="stat-card">
        <mat-card-content>
          <div class="stat-content">
            <mat-icon class="stat-icon sensors">sensors</mat-icon>
            <div class="stat-info">
              <div class="stat-number">{{ deviceStats.sensors }}</div>
              <div class="stat-label">Capteurs</div>
            </div>
          </div>
        </mat-card-content>
      </mat-card>
    </div>
  <mat-card class="filter-card">
    <mat-card-content>
      <div class="filter-section">
        <mat-form-field appearance="outline" class="search-field">
          <mat-label>Rechercher des appareils</mat-label>
          <input matInput 
                 [(ngModel)]="searchTerm" 
                 (ngModelChange)="onSearchChange()"
                 placeholder="Rechercher par nom, vendeur ou modèle">
          <mat-icon matPrefix>search</mat-icon>
          <button mat-icon-button matSuffix *ngIf="searchTerm" (click)="clearSearch()">
            <mat-icon>close</mat-icon>
          </button>
        </mat-form-field>

        <mat-form-field appearance="outline" class="filter-field">
          <mat-label>Catégorie</mat-label>
          <mat-select [(value)]="selectedCategory" (selectionChange)="onCategoryChange()">
            <mat-option value="all">Toutes Catégories</mat-option>
            <mat-option *ngFor="let category of availableCategories" [value]="category">
              {{ category | titlecase }}
            </mat-option>
          </mat-select>
        </mat-form-field>

        <button mat-stroked-button (click)="clearFilters()" class="clear-filters-btn">
          <mat-icon>clear_all</mat-icon>
          Effacer les Filtres
        </button>
      </div>
      
      <div class="view-toggle-section">
        <mat-button-toggle-group [value]="currentView" (change)="toggleView($event.value)">
          <mat-button-toggle value="grid" aria-label="Vue Grille">
            <mat-icon>grid_on</mat-icon> Vue Grille
          </mat-button-toggle>
          <mat-button-toggle value="table" aria-label="Vue Tableau">
            <mat-icon>table_chart</mat-icon> Vue Tableau
          </mat-button-toggle>
        </mat-button-toggle-group>
      </div>

      <div class="filter-results">
        Affichage de {{ filteredDevices.length }} sur {{ devices.length }} appareils
      </div>
    </mat-card-content>
  </mat-card>

  <div class="devices-grid" *ngIf="currentView === 'grid' && filteredDevices.length > 0;">
    <mat-card *ngFor="let device of filteredDevices" 
              class="device-card"
              [ngClass]="'device-type-' + getDeviceCategory(device)">
      
      <mat-card-header>
        <div mat-card-avatar class="device-avatar">
          <mat-icon>{{ getDeviceIcon(device) }}</mat-icon>
        </div>
        <mat-card-title>{{ device.friendly_name }}</mat-card-title>
        <mat-card-subtitle>
          <div class="device-info">
            <span *ngIf="device.definition && device.definition.vendor">{{ device.definition.vendor }}</span>
            <span *ngIf="device.definition && device.definition.model"> {{ device.definition.model }}</span>
            <span *ngIf="!device.definition || (!device.definition.vendor && !device.definition.model)">{{ device.type }}</span>
          </div>
        </mat-card-subtitle>
      </mat-card-header>

      <mat-card-content>
        <div class="sensor-data-grid" *ngIf="getSensorData(device).length > 0">
          <ng-container *ngFor="let sensorData of getSensorData(device); trackBy: trackByKey">
            
            <div *ngIf="sensorData.isControl" class="control-item">
              
              <div *ngIf="sensorData.key === 'state'" class="control-toggle">
                <mat-slide-toggle 
                  [checked]="sensorData.value === 'ON' || sensorData.value === true"
                  [disabled]="!isControlEnabled(device)"
                  (change)="onToggleChange(device, sensorData, $event)">
                  <span class="control-label">
                    <mat-icon>{{ sensorData.icon }}</mat-icon>
                    {{ sensorData.label }}
                  </span>
                </mat-slide-toggle>
                <span class="control-value">{{ formatSensorValue(sensorData) }}</span>
              </div>

              <div *ngIf="sensorData.key === 'brightness' && isDeviceStateOn(device)" class="control-slider">
                <div class="slider-header">
                  <mat-icon>{{ sensorData.icon }}</mat-icon>
                  <span class="control-label">{{ sensorData.label }}</span>
                  <span class="control-value">{{ formatSensorValue(sensorData) }}{{ sensorData.unit || '' }}</span>
                </div>
                <mat-slider 
                  [min]="sensorData.min || 0" 
                  [max]="sensorData.max || 254" 
                  [step]="1"
                  [disabled]="!isControlEnabled(device)"
                  (input)="onBrightnessChange(device, $event)"
                  (change)="onBrightnessChange(device, $event)">
                  <input matSliderThumb [value]="sensorData.value">
                </mat-slider>
              </div>

            </div>

            <div *ngIf="!sensorData.isControl" class="sensor-item">
              
              <div *ngIf="sensorData.type === 'boolean'" class="sensor-boolean">
                <div class="sensor-header">
                  <mat-icon [class]="'sensor-icon-' + sensorData.key">{{ sensorData.icon }}</mat-icon>
                  <span class="sensor-label">{{ sensorData.label }}</span>
                </div>
                <div class="sensor-value" [class]="'boolean-' + sensorData.value">
                  {{ formatSensorValue(sensorData) }}
                </div>
              </div>

              <div *ngIf="sensorData.type === 'number'" class="sensor-numeric">
                <div class="sensor-header">
                  <mat-icon [class]="'sensor-icon-' + sensorData.key">{{ sensorData.icon }}</mat-icon>
                  <span class="sensor-label">{{ sensorData.label }}</span>
                </div>
                <div class="sensor-value">
                  <span class="value-number">{{ formatSensorValue(sensorData) }}</span>
                  <span class="value-unit" *ngIf="sensorData.unit">{{ sensorData.unit }}</span>
                </div>
                
                <mat-progress-bar *ngIf="sensorData.key === 'battery'" 
                                  mode="determinate" 
                                  [value]="sensorData.value"
                                  [color]="sensorData.value > 25 ? 'primary' : 'warn'">
                </mat-progress-bar>
              </div>

              <div *ngIf="sensorData.type === 'string'" class="sensor-text">
                <div class="sensor-header">
                  <mat-icon [class]="'sensor-icon-' + sensorData.key">{{ sensorData.icon }}</mat-icon>
                  <span class="sensor-label">{{ sensorData.label }}</span>
                </div>
                <div class="sensor-value">
                  {{ formatSensorValue(sensorData) }}
                </div>
              </div>

              <div *ngIf="sensorData.type === 'object'" class="sensor-object">
                <div class="sensor-header">
                  <mat-icon>{{ sensorData.icon }}</mat-icon>
                  <span class="sensor-label">{{ sensorData.label }}</span>
                </div>
                <div class="sensor-value object-value">
                  <code>{{ sensorData.value | json }}</code>
                </div>
              </div>

            </div>
          </ng-container>
        </div>

        <div class="device-health" *ngIf="getDeviceState(device)">
          <mat-divider></mat-divider>
          <div class="health-items">
            
            <div class="health-item" *ngIf="getDeviceState(device)?.battery">
              <mat-icon [style.color]="getBatteryColor(getDeviceState(device)?.battery!)">
                {{ getBatteryIcon(getDeviceState(device)?.battery!) }}
              </mat-icon>
              <span>{{ getDeviceState(device)?.battery }}%</span>
            </div>

            <div class="health-item" *ngIf="getDeviceState(device)?.linkquality">
              <mat-icon [style.color]="getSignalColor(getDeviceState(device)?.linkquality!)">
                signal_wifi_4_bar
              </mat-icon>
              <span>{{ getDeviceState(device)?.linkquality }}</span>
            </div>

            <div class="health-item last-seen" *ngIf="getDeviceState(device)?.last_seen">
              <mat-icon>schedule</mat-icon>
              <span>{{ getDeviceState(device)?.last_seen | date:'short' }}</span>
            </div>
          </div>
        </div>

        <div *ngIf="getSensorData(device).length === 0" class="no-data">
          <mat-icon>info_outline</mat-icon>
          <span>Aucune donnée de capteur disponible</span>
        </div>

      </mat-card-content>
    </mat-card>
  </div>

  <div class="devices-table" *ngIf="currentView === 'table' && filteredDevices.length > 0;">
    <div class="mat-elevation-z2 table-container">
      <table mat-table [dataSource]="filteredDevices">

        <ng-container matColumnDef="name">
          <th mat-header-cell *matHeaderCellDef> Nom </th>
          <td mat-cell *matCellDef="let device">
            <div class="device-name-cell">
              <mat-icon class="table-device-icon">{{ getDeviceIcon(device) }}</mat-icon>
              {{ device.friendly_name }}
            </div>
          </td>
        </ng-container>

        <ng-container matColumnDef="category">
          <th mat-header-cell *matHeaderCellDef> Catégorie </th>
          <td mat-cell *matCellDef="let device"> {{ getDeviceCategory(device) | titlecase }} </td>
        </ng-container>

         <!-- <ng-container matColumnDef="status">
          <th mat-header-cell *matHeaderCellDef></th>
          <td mat-cell *matCellDef="let device"> 
          </td>
        </ng-container> -->

        <ng-container matColumnDef="vendorModel">
          <th mat-header-cell *matHeaderCellDef> Vendeur / Modèle </th>
          <td mat-cell *matCellDef="let device">
            <span *ngIf="device.definition && device.definition.vendor">{{ device.definition.vendor }}</span>
            <span *ngIf="device.definition && device.definition.model"> {{ device.definition.model }}</span>
            <span *ngIf="!device.definition || (!device.definition.vendor && !device.definition.model)">N/A</span>
          </td>
        </ng-container>

        <ng-container matColumnDef="lastSeen">
          <th mat-header-cell *matHeaderCellDef> Dernière Vue </th>
          <td mat-cell *matCellDef="let device"> 
            <span *ngIf="getDeviceState(device)?.last_seen">
              {{ getDeviceState(device)?.last_seen | date:'short' }}
            </span>
            <span *ngIf="!getDeviceState(device)?.last_seen">N/A</span>
          </td>
        </ng-container>

        <ng-container matColumnDef="controls">
          <th mat-header-cell *matHeaderCellDef> Contrôles </th>
          <td mat-cell *matCellDef="let device">
            <div class="table-controls">
              <ng-container *ngFor="let sensorData of getSensorData(device); trackBy: trackByKey">
                <div *ngIf="sensorData.isControl">
                  <div *ngIf="sensorData.key === 'state'">
                    <mat-slide-toggle
                      [checked]="sensorData.value === 'ON' || sensorData.value === true"
                      [disabled]="!isControlEnabled(device)"
                      (change)="onToggleChange(device, sensorData, $event)"
                      matTooltip="Activer/Désactiver">
                    </mat-slide-toggle>
                  </div>
                  <div *ngIf="sensorData.key === 'brightness' && isDeviceStateOn(device)">
                    <mat-slider
                      [min]="sensorData.min || 0"
                      [max]="sensorData.max || 254"
                      [step]="1"
                      [disabled]="!isControlEnabled(device)"
                      (input)="onBrightnessChange(device, $event)"
                      (change)="onBrightnessChange(device, $event)"
                      matTooltip="Luminosité">
                      <input matSliderThumb [value]="sensorData.value">
                    </mat-slider>
                  </div>
                </div>
              </ng-container>
              <span *ngIf="hasNoControls(device)">N/A</span>
            </div>
          </td>
        </ng-container>

        <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
        <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
      </table>
    </div>
  </div>

  <!-- <ng-template #noDevices>
    <mat-card class="no-devices-card">
       <mat-card-content>
        <div class="no-devices-content">
          <mat-icon class="large-icon">devices_other</mat-icon>
          <h3>Aucun Appareil Trouvé</h3>
          <p>Aucun appareil Zigbee n'est actuellement disponible.</p>
          <p *ngIf="bridgeState !== 'online'"> Veuillez vous assurer que le pont Zigbee2MQTT est en ligne.
          </p>
        </div>
      </mat-card-content>
    </mat-card>
  </ng-template> -->
</div>