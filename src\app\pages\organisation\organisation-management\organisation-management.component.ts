import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router } from '@angular/router';
import {
  FormsModule,
  ReactiveFormsModule,
  FormGroup,
  FormBuilder,
  Validators,
} from '@angular/forms';
import {
  trigger,
  state,
  style,
  transition,
  animate,
} from '@angular/animations';
import { GenericTableComponent } from '@app/components/generic-table/generic-table.component';
import { ClientApiService } from '@app/core/services/administrative/client.service';
import { Client } from '@app/core/models/client';
import { Organisation } from '@app/core/models/organisation';
import { OrganisationApiService } from '@app/core/services/administrative/organisation.service';
import { id } from '@swimlane/ngx-charts';

@Component({
  selector: 'app-organisation-management',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    GenericTableComponent,
  ],
  templateUrl: './organisation-management.component.html',
  styleUrls: ['./organisation-management.component.css'],
  animations: [
    trigger('fadeIn', [
      state('void', style({ opacity: 0 })),
      transition(':enter', [animate('300ms ease-out', style({ opacity: 1 }))]),
    ]),
  ],
})
export class OrganisationManagementComponent implements OnInit {
  clients: Client[] = [];
  filteredClients: Client[] = [];
  organisations: Organisation[] = [];

  // Table configuration for clients
  headers: string[] = [
    'Nom Complet',
    'Email',
    'Téléphone',
    'Organisation',
    'Équipements Actifs',
    'Équipements Inactifs',
    'Nombre Locaux',
    'Statut',
  ];

  keys: string[] = [
    'NomComplet',
    'email',
    'telephone',
    'organisation.nom',
    'nombreEquipementsActif',
    'nombreEquipementsInactif',
    'nombreLocaux',
    'isEnabled',
  ];

  isLoading: boolean = false;
  searchTerm: string = '';
  showCreateForm: boolean = false;

  // Client creation form
  createClientForm: FormGroup;

  constructor(
    readonly clientApiService: ClientApiService,
    readonly organisationApiService: OrganisationApiService,
    readonly fb: FormBuilder,
    readonly router: Router,
    readonly route: ActivatedRoute
  ) {
    this.createClientForm = this.fb.group({
      nomComplet: ['', Validators.required],
      email: ['', [Validators.required, Validators.email]],
      telephone: ['', Validators.required],
      password: ['', [Validators.required, Validators.minLength(6)]],
      passwordConfirmed: ['', Validators.required],
      idOrganisation: ['', Validators.required], // Changed to use organisation ID
      isEnabled: [true],
    });
  }

  ngOnInit(): void {
    this.loadOrganisations();
    this.loadClients();
  }

  loadOrganisations(): void {
    this.isLoading = true;
    this.organisationApiService.getAll().subscribe({
      next: (organisations: Organisation[]) => {
        this.organisations = organisations;
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading organisations:', error);
        this.isLoading = false;
      },
    });
  }

  loadClients(): void {
    this.isLoading = true;
    this.clientApiService.getAll().subscribe({
      next: (clients) => {
        // Transform the API response to match your interface
        this.clients = clients.map((client) => this.transformClient(client));
        this.filterClients();
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading clients:', error);
        this.isLoading = false;
      },
    });
  }

  // Transform API response to match your interface
  private transformClient(apiClient: any): Client {
    return {
  id: apiClient.Id || apiClient.id,
  nomComplet: apiClient.NomComplet || apiClient.nomComplet,
  email: apiClient.Email || apiClient.email,
  telephone: apiClient.Telephone || apiClient.telephone,
  nombreEquipementsActif: apiClient.NombreEquipementsActif ||
    apiClient.nombreEquipementsActif ||
    0,
  nombreEquipementsInactif: apiClient.NombreEquipementsInactif ||
    apiClient.nombreEquipementsInactif ||
    0,
  logoClient: apiClient.LogoClient || apiClient.logoClient || '',
  isEnabled: apiClient.IsEnabled !== undefined
    ? apiClient.IsEnabled
    : apiClient.isEnabled !== undefined
      ? apiClient.isEnabled
      : true,
  nombreLocaux: apiClient.NombreLocaux || apiClient.nombreLocaux || 0,
  idOrganisation: apiClient.IdOrganisation || apiClient.idOrganisation || '',
  organisation: apiClient.Organisation
    ? {
      nom: apiClient.Organisation.Nom || apiClient.Organisation.nom,
      clients: apiClient.Organisation.Clients ||
        apiClient.Organisation.clients ||
        [],
    }
    : apiClient.organisation ?? { nom: 'N/A', clients: [] },
  sites: apiClient.Sites ?? apiClient.sites ?? [],
  licences: apiClient.Licences ?? apiClient.licences ?? [],
  nomComplet: '',
  email: '',
  telephone: '',
  nombreEquipementsActif: 0,
  nombreEquipementsInactif: 0,
  logoClient: '',
  logoOrganisation: '',
  isEnabled: false,
  nombreLocaux: 0,
  idOrganisation: '',
  sites: [],
  licences: []
};
  }

  filterClients(): void {
    if (!this.searchTerm) {
      this.filteredClients = [...this.clients];
      return;
    }

    const lowerCaseSearchTerm = this.searchTerm.toLowerCase();
    this.filteredClients = this.clients.filter(
      (client) =>
        (client.nomComplet &&
          client.nomComplet.toLowerCase().includes(lowerCaseSearchTerm)) ||
        (client.email &&
          client.email.toLowerCase().includes(lowerCaseSearchTerm)) ||
        (client.telephone &&
          client.telephone.toLowerCase().includes(lowerCaseSearchTerm)) ||
        (client.organisation?.Nom &&
          client.organisation.Nom.toLowerCase().includes(lowerCaseSearchTerm))
    );
  }

  onSearchChange(): void {
    this.filterClients();
  }

  showAddClientForm(): void {
    this.showCreateForm = true;
    this.createClientForm.reset({ isEnabled: true });
  }

  hideAddClientForm(): void {
    this.showCreateForm = false;
    this.createClientForm.reset();
  }

  submitCreateForm(): void {
    if (this.createClientForm.invalid) {
      alert('Veuillez remplir tous les champs obligatoires correctement');
      return;
    }

    const formData = this.createClientForm.value;

    // Check password match
    if (formData.password !== formData.passwordConfirmed) {
      alert('Les mots de passe ne correspondent pas');
      return;
    }

    // Find the selected organisation
    const selectedOrganisation = this.organisations.find(
      (org) => org.id === formData.idOrganisation
    );

    // Prepare client data for API
    const clientData: Client = {
      nomComplet: formData.nomComplet,
      email: formData.email,
      telephone: formData.telephone,
      password: formData.password,
      passwordConfirmed: formData.passwordConfirmed,
      isEnabled: formData.isEnabled,
      nombreEquipementsActif: 0,
      nombreEquipementsInactif: 0,
      nombreLocaux: 0,
      logoClient: '',
      idOrganisation: formData.idOrganisation,
      // organisation: selectedOrganisation ?? null,
      sites: [],
      licences: [],
      logoOrganisation: ''
    };

    this.clientApiService.create(clientData).subscribe({
      next: () => {
        this.loadClients();
        this.hideAddClientForm();
        alert('Client créé avec succès');
      },
      error: (error) => {
        console.error('Error creating client:', error);
        alert('Erreur lors de la création du client');
      },
    });
  }

  handleAction(event: { action: string; row: Client }): void {
    switch (event.action) {
      case 'view':
        this.viewClientDetails(event.row);
        break;
      case 'edit':
        this.editClient(event.row);
        break;
      case 'delete':
        this.deleteClient(event.row);
        break;
      default:
        console.warn('Unknown action:', event.action);
    }
  }

  viewClientDetails(client: Client): void {
    // Navigate to client details - adjust route as needed
    this.router.navigate(['organisation-details/', client.id]);
    console.log('Redirecting to /organisation-details/' + client.id);
  }

  editClient(client: Client): void {
    // Navigate to client edit - adjust route as needed
    this.router.navigate(['organisations/edit/', client.id]);
    console.log('Redirecting to /organisations/edit/' + client.id);
  }

  deleteClient(client: Client): void {
    if (
      confirm(
        `Êtes-vous sûr de vouloir supprimer le client ${client.nomComplet} ?`
      )
    ) {
      // Assuming the delete method uses email as identifier
      this.clientApiService.delete(client.id).subscribe({
        next: () => {
          this.loadClients();
          alert('Client supprimé avec succès');
        },
        error: (error) => {
          console.error('Error deleting client:', error);
          alert('Erreur lors de la suppression du client');
        },
      });
    }
  }
}
