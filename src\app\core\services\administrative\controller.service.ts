import { Injectable } from "@angular/core";
import { ApiService } from "../api.service";
import { Controller } from "@app/core/models/controller";
import { HttpClient } from "@angular/common/http";

@Injectable({ providedIn: 'root' })
export class ControllerApiService extends ApiService<Controller> {
  constructor(public override http: HttpClient) {
    super(http);
    this.setEndpoint("controller");
  }
}