<div class="stats-container">
  <div class="stats-header">
    <h2 class="section-title">Organisations</h2>
  </div>
  <div class="category-grid">
    <app-category-stat-card
    onkeydown=""
      *ngFor="let stat of siteStats"
      [type]="stat.type"
      [count]="stat.count"
      (click)="onTypeClick(stat.type)"
      (viewDetails)="onDetailsClick(stat.type)"  
      [@cardAnimation]>
    </app-category-stat-card>
  </div>
</div>