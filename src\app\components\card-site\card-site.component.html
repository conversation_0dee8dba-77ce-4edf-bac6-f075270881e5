<div
  class="site-card"
  [@cardHover]="cardState"
  (mouseenter)="onMouseEnter()"
  (mouseleave)="onMouseLeave()"
>
  <div class="card-image-container">
    <div class="image-container">
      <img
        [src]="imageUrl"
        [alt]="site.Nom"
        class="site-image"
        (error)="onImageError($event)"
      />
    </div>
  </div>

  <div class="card-header">
    <h3 class="site-title">{{ site.Nom || "Site sans nom" }}</h3>
  </div>

  <div class="card-content">
    <div class="info-section">
      <div class="info-grid">
        <div class="info-item" *ngIf="site.Rue">
          <i class="material-icons icon-small">signpost</i>
          <span>{{ site.Rue }}</span>
        </div>
        <div class="info-item" *ngIf="site.Ville">
          <i class="material-icons icon-small">location_city</i>
          <span>{{ site.Ville }}</span>
        </div>
        <div class="info-item" *ngIf="site.Pays">
          <i class="material-icons icon-small">public</i>
          <span>{{ site.Pays }}</span>
        </div>
      </div>
    </div>
  </div>

  <div class="card-actions">
    <button class="btn btn-primary" [routerLink]="['/site-locals', site.Id]">
      <i class="material-icons">visibility</i>
    </button>
    <button
      class="btn btn-accent"
      (click)="onEdit($event); $event.stopPropagation()"
    >
      <i class="material-icons">edit</i>
    </button>
    <button
      class="btn btn-danger"
      (click)="onDelete($event); $event.stopPropagation()"
    >
      <i class="material-icons">delete</i>
    </button>
  </div>
</div>