import { Licence } from "./licence";
import { AuditModel } from "./models-audit/audit-model";
import { Organisation } from "./organisation";
import { Site } from "./site";

export interface Client extends AuditModel {
  nomComplet: string;
  email: string;
  password?: string;
  passwordConfirmed?: string;
  telephone: string;
  nombreEquipementsActif: number;
  nombreEquipementsInactif: number;
  logoClient: string;
  logoOrganisation: string;
  isEnabled: boolean;
  nombreLocaux: number;
  idOrganisation: string;
  organisation?: Organisation;
  sites: Site[];
  licences: Licence[];
  id?: string; // Optional, from API
}