import { Injectable } from "@angular/core";
import { ApiService } from "../api.service";
import { HttpClient } from "@angular/common/http";
import { ControllerServeur } from "@app/core/models/ControllerServeur";

@Injectable({ providedIn: 'root' })
export class ControllerServeurControllerApiService extends ApiService<ControllerServeur> {
  constructor(public override http: HttpClient) {
    super(http);
    this.setEndpoint("controller-serveur");
  }
}