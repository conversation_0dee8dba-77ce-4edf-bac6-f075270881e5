.stat-card {
  background-color: white;
  border-radius: 0.75rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
  border: 1px solid #e5e7eb;
  overflow: hidden;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.stat-color-bar {
  height: 0.5rem;
  transition: opacity 0.3s ease;
}

.stat-card:hover .stat-color-bar {
  opacity: 0.9;
}

.stat-content {
  padding: 1.5rem;
}

.stat-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1rem;
}

.stat-icon-container {
  padding: 0.75rem;
  border-radius: 0.75rem;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: transform 0.2s ease;
}

.stat-icon-container:hover {
  transform: scale(1.05);
}

.stat-icon {
  width: 1.5rem;
  height: 1.5rem;
  font-size: 1.5rem;
  color: white;
}

.stat-values {
  text-align: right;
    transition: all 0.3s ease;

}

.stat-value {
  font-size: 1.75rem;
  font-weight: 700;
  color: #111827;
  font-family: 'Montserrat', sans-serif;
  line-height: 1.2;
}

.stat-subtitle {
  font-size: 0.875rem;
  color: #6b7280;
  font-family: 'Lato', sans-serif;
  margin-top: 0.25rem;
}

.stat-title {
  color: #374151;
  font-weight: 600;
  font-size: 1rem;
  margin-top: 0.5rem;
  font-family: 'Montserrat', sans-serif;
}

/* Status colors */
.stat-green {
  background-color: #10b981;
}

.stat-yellow {
  background-color: #f59e0b;
}

.stat-gray {
  background-color: #6b7280;
}

.stat-red {
  background-color: #ef4444;
}

/* Color bar gradients */
.bg-emerald-500 {
  background: linear-gradient(to right, #059669, #34d399);
}

.bg-yellow-500 {
  background: linear-gradient(to right, #d97706, #fbbf24);
}

.bg-gray-500 {
  background: linear-gradient(to right, #4b5563, #9ca3af);
}

.bg-red-500 {
  background: linear-gradient(to right, #dc2626, #f87171);
}

.stat-value:hover {
  transform: scale(1.05);
}