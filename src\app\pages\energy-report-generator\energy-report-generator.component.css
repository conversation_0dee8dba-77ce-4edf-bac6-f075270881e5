/* Main Container */
.report-generator-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 24px;
  font-family: 'Roboto', sans-serif;
}

/* Header Section */
.header-section {
  text-align: center;
  margin-bottom: 40px;
  padding: 32px;
  background: linear-gradient(135deg, #4CAF50 0%, #2E7D32 100%);
  border-radius: 16px;
  color: white;
}

.page-title {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  font-size: 2.5rem;
  font-weight: 300;
  margin: 0 0 12px;
}

.page-title mat-icon {
  font-size: 3rem;
  width: 3rem;
  height: 3rem;
}

.page-subtitle {
  font-size: 1.1rem;
  opacity: 0.9;
  margin: 0;
  font-weight: 300;
}

/* Form Container */
.form-container {
  margin-bottom: 32px;
}

.form-card {
  background: white;
  border-radius: 16px;
  padding: 32px;
  box-shadow: 0 8px 32px rgba(0,0,0,0.1);
  border: 1px solid #e0e0e0;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 12px;
  color: #2E7D32;
  font-size: 1.5rem;
  font-weight: 500;
  margin: 0 0 24px;
  padding-bottom: 12px;
  border-bottom: 2px solid #E8F5E8;
}

.form-section {
  margin-bottom: 32px;
}

.form-label {
  display: block;
  font-weight: 500;
  color: #333;
  margin-bottom: 12px;
  font-size: 1rem;
}

/* Organization Search */
.search-container {
  position: relative;
}

.search-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.search-icon {
  position: absolute;
  left: 16px;
  color: #666;
  z-index: 2;
}

.search-input {
  width: 100%;
  padding: 16px 16px 16px 48px;
  border: 2px solid #e0e0e0;
  border-radius: 12px;
  font-size: 1rem;
  transition: all 0.3s ease;
  background: white;
}

.search-input:focus {
  outline: none;
  border-color: #4CAF50;
  box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.1);
}

.dropdown-container {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0,0,0,0.15);
  z-index: 1000;
  max-height: 300px;
  overflow-y: auto;
  margin-top: 4px;
}

.dropdown-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  cursor: pointer;
  outline: none;
  transition: background-color 0.2s ease;
  border-bottom: 1px solid #f5f5f5;
}

.dropdown-item:hover {
  background-color: #f8f9fa;
}

.dropdown-item:last-child {
  border-bottom: none;
}

.org-info .org-name {
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.org-info .org-details {
  font-size: 0.85rem;
  color: #666;
}

.select-icon {
  color: #4CAF50;
}

.no-results {
  padding: 20px;
  text-align: center;
  color: #666;
  font-style: italic;
}

/* Selected Organization */
.selected-org {
  margin-top: 16px;
}

.org-card-mini {
  background: #f8f9fa;
  border: 2px solid #4CAF50;
  border-radius: 12px;
  padding: 20px;
  transition: all 0.3s ease;
}

.org-header {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 16px;
}

.org-icon {
  color: #4CAF50;
  font-size: 2rem;
  width: 2rem;
  height: 2rem;
}

.org-header .org-info h3 {
  margin: 0;
  color: #2E7D32;
  font-size: 1.2rem;
  font-weight: 500;
}

.org-header .org-info p {
  margin: 4px 0 0;
  color: #666;
  font-size: 0.9rem;
}

.clear-btn {
  background: none;
  border: none;
  cursor: pointer;
  color: #999;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s ease;
  margin-left: auto;
}

.clear-btn:hover {
  background-color: #f44336;
  color: white;
}

.org-stats {
  display: flex;
  gap: 24px;
  flex-wrap: wrap;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #555;
  font-size: 0.9rem;
}

.stat-icon {
  color: #4CAF50;
  font-size: 1.2rem;
  width: 1.2rem;
  height: 1.2rem;
}

/* Date Range */
.date-range-container {
  display: flex;
  align-items: center;
  gap: 16px;
  flex-wrap: wrap;
}

.date-field {
  flex: 1;
  min-width: 200px;
}

.date-separator {
  display: flex;
  align-items: center;
  color: #4CAF50;
  margin: 0 8px;
}

/* Format Selection */
.format-selection {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.format-option {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 20px;
  border: 2px solid #e0e0e0;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: white;
}

.format-option:hover {
  border-color: #4CAF50;
  background-color: #f8f9fa;
}

.format-option.selected {
  border-color: #4CAF50;
  background-color: #e8f5e8;
}

.format-icon {
  color: #4CAF50;
  font-size: 2rem;
  width: 2rem;
  height: 2rem;
}

.format-info h4 {
  margin: 0 0 4px;
  color: #333;
  font-size: 1.1rem;
  font-weight: 500;
}

.format-info p {
  margin: 0;
  color: #666;
  font-size: 0.9rem;
}

.check-icon {
  color: #4CAF50;
  margin-left: auto;
}

/* Form Actions */
.form-actions {
  display: flex;
  justify-content: space-between;
  gap: 16px;
  margin-top: 40px;
  padding-top: 24px;
  border-top: 1px solid #e0e0e0;
}

.btn {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 500;
  transition: all 0.3s ease;
  text-decoration: none;
  min-width: 140px;
  justify-content: center;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background-color: #4CAF50;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background-color: #3e8e41;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
}

.btn-secondary {
  background-color: #f5f5f5;
  color: #333;
  &:hover {
    background-color: #e8e8e8;
  transform: translateY(-2px);
  }
}

/* Preview Container */
.preview-container {
  margin-top: 32px;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0,0,0,0.1);
}

.preview-title {
  display: flex;
  align-items: center;
  gap: 12px;
  color: #2E7D32;
  font-size: 1.5rem;
  font-weight: 500;
  margin: 0;
}

/* Report Preview */
.report-preview {
  background: white;
  border-radius: 16px;
  padding: 40px;
  box-shadow: 0 8px 32px rgba(0,0,0,0.1);
  border: 1px solid #e0e0e0;
}

.report-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 40px;
  padding-bottom: 24px;
  border-bottom: 3px solid #4CAF50;
}

.report-title h1 {
  color: #2E7D32;
  font-size: 2.5rem;
  font-weight: 300;
  margin: 0 0 8px;
  letter-spacing: -1px;
}

.report-period {
  color: #666;
  font-size: 1.1rem;
  font-weight: 300;
}

.report-logo {
  text-align: center;
}

.logo-icon {
  color: #4CAF50;
  font-size: 4rem;
  width: 4rem;
  height: 4rem;
}

.report-section {
  margin-bottom: 40px;
}

.section-header {
  display: flex;
  align-items: center;
  gap: 12px;
  color: #2E7D32;
  font-size: 1.4rem;
  font-weight: 500;
  margin: 0 0 20px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e8f5e8;
}

/* Info Grid */
.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 16px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  padding: 12px 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #4CAF50;
}

.info-item label {
  font-weight: 500;
  color: #555;
}

.info-item span {
  color: #333;
  font-weight: 400;
}

/* Summary Grid */
.summary-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.summary-card {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 24px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0,0,0,0.08);
  border: 1px solid #f0f0f0;
  transition: transform 0.2s ease;
}

.summary-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0,0,0,0.12);
}

.summary-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  border-radius: 12px;
  color: white;
}

.summary-icon.positive {
  background-color: #4CAF50;
}

.summary-icon.warning {
  background-color: #FF9800;
}

.summary-icon.negative {
  background-color: #f44336;
}

.summary-value {
  font-size: 1.4rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.summary-label {
  color: #666;
  font-size: 0.9rem;
}

/* Data Table */
.data-table {
  overflow-x: auto;
  border-radius: 12px;
  border: 1px solid #e0e0e0;
}

.data-table table {
  width: 100%;
  border-collapse: collapse;
  background: white;
}

.data-table th {
  background-color: #4CAF50;
  color: white;
  padding: 16px 12px;
  text-align: left;
  font-weight: 500;
  border-bottom: 2px solid #3e8e41;
}

.data-table td {
  padding: 14px 12px;
  border-bottom: 1px solid #f0f0f0;
  color: #333;
}

.data-table tr:hover {
  background-color: #f8f9fa;
}

.data-table tr:last-child td {
  border-bottom: none;
}

/* Savings Section */
.savings-highlight {
  text-align: center;
  padding: 32px;
  background: linear-gradient(135deg, #4CAF50, #2E7D32);
  color: white;
  border-radius: 16px;
  box-shadow: 0 8px 24px rgba(76, 175, 80, 0.3);
}

.savings-amount {
  font-size: 3rem;
  font-weight: 300;
  margin-bottom: 8px;
  text-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.savings-description {
  font-size: 1.1rem;
  opacity: 0.9;
  font-weight: 300;
}

/* Responsive Design */
@media (max-width: 768px) {
  .report-generator-container {
    padding: 16px;
  }
  
  .form-card {
    padding: 24px;
  }
  
  .page-title {
    font-size: 2rem;
  }
  
  .format-selection {
    grid-template-columns: 1fr;
  }
  
  .date-range-container {
    flex-direction: column;
    align-items: stretch;
  }
  
  .date-separator {
    transform: rotate(90deg);
    margin: 8px 0;
  }
  
  .form-actions {
    flex-direction: column;
  }
  
  .preview-header {
    flex-direction: column;
    gap: 16px;
    text-align: center;
      display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  margin-bottom: 24px;
  }
  
  .report-header {
    flex-direction: column;
    text-align: center;
    gap: 24px;
  }
  
  .summary-grid {
    grid-template-columns: 1fr;
  }
  
  .info-grid {
    grid-template-columns: 1fr;
  }
  
  .org-stats {
    flex-direction: column;
    gap: 12px;
  }
}

/* Loading Spinner Styles */
mat-spinner {
  margin-right: 8px;
}

/* Snackbar Styles */
.success-snackbar {
  background-color: #4CAF50 !important;
  color: white !important;
}

.error-snackbar {
  background-color: #f44336 !important;
  color: white !important;
}

.action-buttons {
  display: flex;
  gap: 12px;
}


.sites-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.site-address {
  font-size: 0.9em;
  color: #666;
  margin-left: 12px;
}


.dropdown-item:focus {
  background-color: rgba(0, 0, 0, 0.04);
}

.dropdown-item:focus-visible {
  outline: 2px solid #1976d2;
  outline-offset: -2px;
}



/* Material Form Field Customization */
::ng-deep .mat-form-field-appearance-outline .mat-form-field-outline-thick {
  color: #4CAF50;
}

::ng-deep .mat-form-field-appearance-outline.mat-focused .mat-form-field-outline-thick {
  color: #4CAF50;
}

::ng-deep .mat-form-field-appearance-outline.mat-focused .mat-form-field-label {
  color: #4CAF50;
}

/* Animation Classes */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.form-card,
.report-preview {
  animation: fadeIn 0.5s ease-out;
}

/* Print Styles */
@media print {
  .preview-header,
  .form-actions {
    display: none !important;
  }
  
  .report-preview {
    box-shadow: none;
    border: none;
    padding: 0;
  }
  
  .summary-card {
    break-inside: avoid;
  }
  
  .data-table {
    break-inside: avoid;
  }
}