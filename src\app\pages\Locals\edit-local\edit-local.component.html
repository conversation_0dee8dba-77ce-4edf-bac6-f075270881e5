<div class="edit-form-container">
  <div class="edit-form-card">
    <h2 class="form-title">Modifier le Local</h2>
    
<form [formGroup]="editLocalForm" (ngSubmit)="onSubmit()" (keydown.enter)="$event.preventDefault()">
      <div class="form-grid">
       <div class="form-group">
  <label for="type">Type de local</label>
  <input
    id="type"
    list="localTypesList"
    formControlName="type"
    class="form-control"
    placeholder="Sélectionner ou saisir un type">
  <datalist id="localTypesList">
    <option *ngFor="let type of localTypes" [value]="type.value">
      {{type.label}}
    </option>
  </datalist>
</div>

        <div class="form-group">
          <label for="etage">Étage</label>
          <input type="number" id="etage" formControlName="etage">
        </div>

        <div class="form-group">
          <label for="surface">Surface (m²)</label>
          <input type="number" id="surface" formControlName="surface">
        </div>

        <div class="form-group">
          <label for="hauteurSousPlafond">Hauteur sous plafond (m)</label>
          <input type="number" id="hauteurSousPlafond" formControlName="hauteurSousPlafond">
        </div>

        <div class="form-group">
          <label for="capacitePersonnes">Capacité (personnes)</label>
          <input type="number" id="capacitePersonnes" formControlName="capacitePersonnes">
        </div>

        <div class="form-group">
          <label for="temperatureCible">Température cible (°C)</label>
          <input type="number" id="temperatureCible" formControlName="temperatureCible">
        </div>

        <div class="form-group">
          <label for="consommationElectriqueMensuelle">Consommation mensuelle (kWh)</label>
          <input type="number" id="consommationElectriqueMensuelle" formControlName="consommationElectriqueMensuelle">
        </div>

        <div class="form-group">
          <label for="dateDerniereMaintenance">Dernière maintenance</label>
          <input type="date" id="dateDerniereMaintenance" formControlName="dateDerniereMaintenance">
        </div>

        <div class="form-group full-width">
          <label for="description">Description</label>
          <textarea id="description" formControlName="description" rows="3"></textarea>
        </div>
      </div>

        <!-- Add these fields inside the form-grid div, before the form-actions -->
<div class="form-group">
  <label for="architecture2D">Plan d'architecture 2D</label>
  <div class="file-upload">
    <input 
      type="file" 
      id="architecture2D" 
      (change)="onArchitecture2DSelected($event)"
      accept="image/*"
      class="form-control">
    <div class="preview" *ngIf="architecture2DPreview || local.imageArchitecture2D">
      <img [src]="architecture2DPreview || local.imageArchitecture2D" alt="Architecture 2D">
    </div>
  </div>
</div>

<div class="form-group">
  <label for="localeImage">Photo du local</label>
  <div class="file-upload">
    <input 
      type="file" 
      id="localeImage" 
      (change)="onLocaleImageSelected($event)"
      accept="image/*"
      class="form-control">
    <div class="preview" *ngIf="localeImagePreview || local.imageLocale">
      <img [src]="localeImagePreview || local.imageLocale" alt="Local Image">
    </div>
  </div>
</div>

<div class="form-group full-width">
  <label for="tags">Tags</label>
  <div class="tags-input">
    <input 
      type="text" 
      id="newTag"
      [(ngModel)]="newTag"
      [ngModelOptions]="{standalone: true}"
      (keyup.enter)="$event.preventDefault();addTag()"
      placeholder="Ajouter un tag et appuyer sur Entrée">
    <div class="tags-list" role="list">
      <span class="tag" *ngFor="let tag of tags" role="listitem">
        {{ tag }}
        <i class="material-icons remove-tag" 
           (click)="removeTag(tag)"
           (keydown.enter)="removeTag(tag)"
           (keydown.space)="removeTag(tag)"
           tabindex="0"
           role="button"
           [attr.aria-label]="'Supprimer le tag ' + tag">
          close
        </i>
      </span>
    </div>
  </div>
</div>

<!-- Update the image previews -->
<div class="preview" *ngIf="architecture2DPreview || local.imageArchitecture2D">
  <img [src]="architecture2DPreview || local.imageArchitecture2D" 
       [alt]="'Plan architectural du local ' + local.id">
</div>

<div class="preview" *ngIf="localeImagePreview || local.imageLocale">
  <img [src]="localeImagePreview || local.imageLocale" 
       [alt]="'Vue du local ' + local.id">
</div>

      <div class="form-actions">
        <button type="button" class="btn btn-secondary" (click)="onCancel()">Annuler</button>
        <button type="submit" class="btn btn-primary" [disabled]="!editLocalForm.valid">Enregistrer</button>
      </div>
    </form>
  </div>
</div>