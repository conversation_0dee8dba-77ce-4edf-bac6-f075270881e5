import { Injectable } from "@angular/core";
import { ApiService } from "../api.service";
import { HttpClient } from "@angular/common/http";
import { ControllerServerController } from "@app/core/models/controllerServerController";

@Injectable({ providedIn: 'root' })
export class ControllerServerControllerApiService extends ApiService<ControllerServerController> {
  constructor(public override http: HttpClient) {
    super(http);
    this.setEndpoint("controller-serveur-controller");
  }
}