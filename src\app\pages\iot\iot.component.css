/* General container styling */
.container {
  max-width: 1200px;
  margin: 30px auto;
  padding: 25px;
  background: linear-gradient(145deg, #ffffff, #f9f9f9);
  border-radius: 12px;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.08);
}

/* Header section */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  animation: slideIn 0.5s ease-out;
}

.page-title {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 28px;
  font-weight: 700;
  color: #2d3748;
  margin: 0;
  letter-spacing: 0.5px;
}

.title-icon {
  font-size: 25px;
  color: #4CAF50;
  background: linear-gradient(45deg, #4CAF50, #81C784);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}


.add-button {
  background: var(--installation) !important;
  color: white !important;
  padding: 10px 20px !important;
  border-radius: 8px !important;
  box-shadow: 0 3px 10px rgba(76, 175, 80, 0.3) !important;
  transition: all 0.3s ease !important;
  display: flex !important;
  align-items: center !important;
  gap: 8px !important;
  border: none !important;
}

.add-button:hover {
  transform: translateY(-3px) !important;
  box-shadow: 0 6px 15px rgba(76, 175, 80, 0.5) !important;
  background: var(--primary) !important;
}

.add-button mat-icon {
  color: white !important;
  margin-right: 4px !important;
}

.add-button:disabled {
  background: #cccccc !important;
  cursor: not-allowed !important;
  transform: none !important;
  box-shadow: none !important;
}

.action-icon {
  margin-right: 8px;
}

/* Pagination controls */
.pagination-controls {
  display: flex;
  justify-content: center;
  gap: 10px;
  margin-bottom: 20px;
}

:host ::ng-deep .p-button-icon-only.p-button-text {
  color: #4CAF50;
  transition: all 0.3s ease;
}

:host ::ng-deep .p-button-icon-only.p-button-text:hover {
  background-color: #e8f5e9;
  transform: scale(1.1);
}

:host ::ng-deep .p-button-icon-only.p-button-text:disabled {
  color: #a0aec0;
  cursor: not-allowed;
}

/* Table styling */
:host ::ng-deep .sensor-table {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

:host ::ng-deep .sensor-table .p-datatable-header {
  background: linear-gradient(45deg, #f5f5f5, #eceff1);
  border-bottom: 2px solid #e2e8f0;
  padding: 15px;
}

:host ::ng-deep .sensor-table th {
  background: linear-gradient(45deg, #f5f5f5, #eceff1);
  color: #2d3748;
  font-weight: 600;
  padding: 15px;
  border-bottom: 2px solid #e2e8f0;
  text-align: left;
}

:host ::ng-deep .sensor-table .sensor-row {
  transition: all 0.3s ease;
}

:host ::ng-deep .sensor-table .sensor-row:hover {
  background-color: #f9f9f9;
  transform: translateY(-2px);
}

:host ::ng-deep .sensor-table td {
  padding: 15px;
  color: #4a5568;
  border-bottom: 1px solid #edf2f7;
}

.image-column {
  width: 120px;
}

.actions-column {
  width: 150px;
}

.image-cell {
  text-align: center;
}

.sensor-image {
  width: 60px;
  height: 60px;
  object-fit: cover;
  border-radius: 8px;
  border: 2px solid #e8f5e9;
  transition: transform 0.3s ease;
}

.sensor-row:hover .sensor-image {
  transform: scale(1.1);
}

.actions-cell {
  display: flex;
  gap: 10px;
  justify-content: center;
}

.action-edit {
  color: #4CAF50;
  transition: all 0.3s ease;
}

.action-edit:hover {
  background-color: #e8f5e9;
  transform: scale(1.1);
}

.action-delete {
  color: #ef5350;
  transition: all 0.3s ease;
}

.action-delete:hover {
  background-color: #ffebee;
  transform: scale(1.1);
}

/* Pagination Styles */
:host ::ng-deep .p-paginator {
  background: #ffffff;
  border-top: 1px solid #e2e8f0;
  padding: 1rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-radius: 0 0 12px 12px;
}

:host ::ng-deep .p-paginator .p-paginator-current {
  margin-left: auto;
  color: #64748b;
  font-weight: 500;
}

:host ::ng-deep .p-paginator .p-paginator-pages .p-paginator-page {
  min-width: 2.5rem;
  height: 2.5rem;
  margin: 0 0.125rem;
  border-radius: 8px;
  transition: all 0.2s ease;
}

:host ::ng-deep .p-paginator .p-paginator-pages .p-paginator-page.p-highlight {
  background: var(--installation);
  color: white;
}

:host ::ng-deep .p-paginator .p-paginator-first,
:host ::ng-deep .p-paginator .p-paginator-prev,
:host ::ng-deep .p-paginator .p-paginator-next,
:host ::ng-deep .p-paginator .p-paginator-last {
  min-width: 2.5rem;
  height: 2.5rem;
  border-radius: 8px;
  margin: 0 0.125rem;
  transition: all 0.2s ease;
}

.pagination-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

/* Add these styles for the rows per page dropdown */
.rows-per-page {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-left: 16px;
  padding: 8px;
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.rows-per-page label {
  color: #64748b;
  font-weight: 500;
  font-size: 0.9rem;
}

:host ::ng-deep .rows-dropdown {
  .p-dropdown {
    position: relative;
    z-index: 1000; /* Increase z-index to appear above card */
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    background: #ffffff;
    
    &:hover {
      border-color: var(--installation);
    }
    
    &.p-focus {
      border-color: var(--installation);
      box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.1);
    }
  }

  .p-dropdown-panel {
    position: absolute; /* Higher z-index than the dropdown trigger */
    border: none;
    border-radius: 8px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    margin-top: 4px;
    background: #ffffff;
    transform-origin: top;
    animation: dropdownOpen 0.2s ease-out;
  }

  .p-dropdown-label {
    padding: 8px 12px;
    font-size: 0.9rem;
    color: #1e293b;
  }

  .p-dropdown-trigger {
    width: 32px;
    background: transparent;
    color: #64748b;
  }


  .p-dropdown-items {
    padding: 8px 0;
  }

  .p-dropdown-item {
    padding: 8px 16px;
    color: #4a5568;
    transition: all 0.2s ease;

    &:hover {
      background: #f8fafc;
      color: var(--installation);
    }

    &.p-highlight {
      background: #e8f5e9;
      color: var(--installation);
      font-weight: 500;
    }
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .rows-per-page {
    margin: 8px 0;
    width: 100%;
    justify-content: center;
  }

  :host ::ng-deep .rows-dropdown .p-dropdown {
    width: 140px;
  }
}

.pagination-label {
  color: #64748b;
  font-weight: 500;
}

.pagination-value {
  color: #1e293b;
  font-weight: 600;
}

.pagination-size-selector {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.size-label {
  color: #64748b;
  font-weight: 500;
}

:host ::ng-deep .p-dropdown {
  min-width: 100px;
  height: 2.5rem;
}

:host ::ng-deep .p-dropdown-panel {
  border-radius: 8px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}



/* Responsive adjustments */
@media (max-width: 768px) {
  :host ::ng-deep .p-paginator {
    flex-wrap: wrap;
    gap: 1rem;
    justify-content: center;
  }

  .pagination-info,
  .pagination-size-selector {
    width: 100%;
    justify-content: center;
  }

  :host ::ng-deep .p-paginator .p-paginator-current {
    margin: 0;
    width: 100%;
    text-align: center;
  }
}

/* Hover effects */
:host ::ng-deep .p-paginator .p-paginator-pages .p-paginator-page:not(.p-highlight):hover,
:host ::ng-deep .p-paginator .p-paginator-first:not(.p-disabled):hover,
:host ::ng-deep .p-paginator .p-paginator-prev:not(.p-disabled):hover,
:host ::ng-deep .p-paginator .p-paginator-next:not(.p-disabled):hover,
:host ::ng-deep .p-paginator .p-paginator-last:not(.p-disabled):hover {
  background: #e2e8f0;
  color: #1e293b;
  transform: translateY(-1px);
}

/* Disabled state */
:host ::ng-deep .p-paginator .p-paginator-first.p-disabled,
:host ::ng-deep .p-paginator .p-paginator-prev.p-disabled,
:host ::ng-deep .p-paginator .p-paginator-next.p-disabled,
:host ::ng-deep .p-paginator .p-paginator-last.p-disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* No data message */
.no-data {
  text-align: center;
  padding: 30px;
  color: #718096;
  animation: fadeIn 0.5s ease-out;
}

.no-data-icon {
  font-size: 60px;
  color: #4CAF50;
  margin-bottom: 15px;
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive design */
@media (max-width: 768px) {
  .container {
    padding: 15px;
  }

  .page-title {
    font-size: 24px;
  }

  .title-icon {
    font-size: 28px;
  }

  .add-button {
    padding: 8px 16px;
  }

  .sensor-image {
    width: 50px;
    height: 50px;
  }

  :host ::ng-deep .sensor-table th,
  :host ::ng-deep .sensor-table td {
    padding: 10px;
  }

  .image-column {
    width: 100px;
  }

  .actions-column {
    width: 120px;
  }
}

@media (max-width: 480px) {
  .header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }

  .add-button {
    width: 100%;
  }

  .pagination-controls {
    flex-wrap: wrap;
  }
}

@keyframes dropdownOpen {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Update container style to ensure proper stacking context */
.container {
  position: relative;
  z-index: 1;
}

/* Update rows-per-page container */
.rows-per-page {
  position: relative;
  z-index: 1000;
}