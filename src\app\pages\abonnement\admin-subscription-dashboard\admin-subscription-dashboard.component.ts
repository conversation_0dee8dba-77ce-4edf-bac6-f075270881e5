import { Component, CUSTOM_ELEMENTS_SCHEMA, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { MatIconModule } from '@angular/material/icon'; // ✅ Replaced Lucide
import { StatCardComponent } from '../stat-card/stat-card.component';
import { SubscriptionTableComponent } from '../subscription-table/subscription-table.component';
import { ActionModalComponent } from '../action-modal/action-modal.component';
import { OrganisationService, Organisation } from '../../../core/services/organisation.service';

export interface Subscription {
  id: number;
  companyName: string;
  contactEmail: string;
  contactName: string;
  startDate: string;
  status: 'active' | 'paused' | 'inactive' | 'banned';
  sites: number;
  equipments: number;
  consumption: number;
  lastActivity: string;
  plan: string;
}

interface StatusOption {
  value: string;
  label: string;
  count: number;
}

interface StatusConfig {
  color: string;
  icon: string;
  label: string;
}

interface ActionEvent {
  subscription: Subscription;
  action: string;
}

interface Action {
  type: string;
  label: string;
  icon: string;
  color: string;
}

@Component({
  selector: 'app-admin-subscription-dashboard',
  templateUrl: './admin-subscription-dashboard.component.html',
  styleUrls: ['./admin-subscription-dashboard.component.css'],
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatIconModule, // ✅ added Material Icons
    StatCardComponent,
    SubscriptionTableComponent,
    ActionModalComponent
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})
export class AdminSubscriptionDashboardComponent implements OnInit {
  searchTerm: string = '';
  statusFilter: string = 'all';
  showActionModal: boolean = false;
  selectedSubscription: Subscription | null = null;
  actionType: string = '';
  organisations: Organisation[] = [];
  subscriptions: Subscription[] = []; // Will be populated from organisations

    currentPage: number = 1;
  pageSize: number = 4;
  totalPages: number = 1;
  Math = Math; // For use in the template

  constructor(readonly organisationService: OrganisationService) {
    console.log('Initial Subscriptions:', this.subscriptions);
  }

    ngOnInit() {
    this.loadOrganisations();
  }

    private loadOrganisations() {
    this.organisationService.getOrganisations().subscribe({
      next: (orgs) => {
        this.organisations = orgs;
        this.updateSubscriptionsFromOrganisations();
      },
      error: (error) => {
        console.error('Error loading organisations:', error);
      }
    });
  }

 private updateSubscriptionsFromOrganisations() {
  this.subscriptions = this.organisations.map(org => {
    const status = this.getRandomStatus();
    return {
      id: org.id,
      companyName: org.nom,
      contactEmail: org.emailAddress ?? '',
      contactName: org.nom,
      startDate: new Date().toISOString(),
      status: status,  // This is now properly typed
      sites: org.sites?.length ?? 0,
      equipments: org.nombreEquipement,
      consumption: org.consommation,
      lastActivity: new Date().toISOString(),
      plan: this.getPlanBasedOnConsumption(org.consommation)
    } as Subscription;  // Type assertion to ensure it matches Subscription interface
  });
}

private getRandomStatus(): 'active' | 'paused' | 'inactive' | 'banned' {
  const statuses = ['active', 'paused', 'inactive', 'banned'] as const;
  const randomIndex = Math.floor(Math.random() * statuses.length);
  return statuses[randomIndex];
}

  private getPlanBasedOnConsumption(consumption: number): string {
    if (consumption > 100000) return 'Enterprise';
    if (consumption > 50000) return 'Premium';
    if (consumption > 10000) return 'Standard';
    return 'Basic';
  }

  get activeSubscriptionsCount(): number {
    return this.subscriptions.filter(s => s.status === 'active').length;
  }

  get pausedSubscriptionsCount(): number {
    return this.subscriptions.filter(s => s.status === 'paused').length;
  }

  get inactiveSubscriptionsCount(): number {
    return this.subscriptions.filter(s => s.status === 'inactive').length;
  }

  get bannedSubscriptionsCount(): number {
    return this.subscriptions.filter(s => s.status === 'banned').length;
  }

  get statusOptions(): StatusOption[] {
    return [
      { value: 'all', label: 'Tous les statuts', count: this.subscriptions.length },
      { value: 'active', label: 'Actifs', count: this.activeSubscriptionsCount },
      { value: 'paused', label: 'En pause', count: this.pausedSubscriptionsCount },
      { value: 'inactive', label: 'Inactifs', count: this.inactiveSubscriptionsCount },
      { value: 'banned', label: 'Bannis', count: this.bannedSubscriptionsCount }
    ];
  }

  getStatusConfig(status: string): StatusConfig {
    const configs: { [key: string]: StatusConfig } = {
      active: { color: 'status-active', icon: 'check_circle', label: 'Actif' },
      paused: { color: 'status-paused', icon: 'schedule', label: 'En pause' },
      inactive: { color: 'status-inactive', icon: 'cancel', label: 'Inactif' },
      banned: { color: 'status-banned', icon: 'person_off', label: 'Banni' }
    };
    return configs[status] ?? configs['inactive'];
  }

  getAvailableActions(status: string): Action[] {
    const actions: { [key: string]: Action[] } = {
      active: [
        { type: 'pause', label: 'Mettre en pause', icon: 'pause', color: 'action-yellow' },
        { type: 'deactivate', label: 'Désactiver', icon: 'stop', color: 'action-gray' },
        { type: 'ban', label: 'Bannir', icon: 'block', color: 'action-red' }
      ],
      paused: [
        { type: 'activate', label: 'Réactiver', icon: 'play_arrow', color: 'action-green' },
        { type: 'deactivate', label: 'Désactiver', icon: 'stop', color: 'action-gray' },
        { type: 'ban', label: 'Bannir', icon: 'block', color: 'action-red' }
      ],
      inactive: [
        { type: 'activate', label: 'Activer', icon: 'play_arrow', color: 'action-green' },
        { type: 'ban', label: 'Bannir', icon: 'block', color: 'action-red' }
      ],
      banned: [
        { type: 'activate', label: 'Réactiver', icon: 'play_arrow', color: 'action-green' }
      ]
    };
    return actions[status] || [];
  }

  handleAction(subscription: Subscription, action: string): void {
    this.selectedSubscription = subscription;
    this.actionType = action;
    this.showActionModal = true;
  }

confirmAction(): void {
  if (!this.selectedSubscription) return;

  const statusMap: { [key: string]: 'active' | 'paused' | 'inactive' | 'banned' } = {
    activate: 'active',
    pause: 'paused',
    deactivate: 'inactive',
    ban: 'banned'
  };

  const newStatus = statusMap[this.actionType];
  if (newStatus) {
    this.subscriptions = this.subscriptions.map(sub =>
      sub.id === this.selectedSubscription!.id 
        ? { ...sub, status: newStatus } 
        : sub
    );
  }

  this.showActionModal = false;
  this.selectedSubscription = null;
  this.actionType = '';
}



getTotalSites(): number {
  return this.organisations.reduce((total, org) => total + (org.sites?.length ?? 0), 0);
}

getTotalEquipments(): number {
  return this.organisations.reduce((total, org) => total + org.nombreEquipement, 0);
}

getTotalConsumption(): number {
  return Math.round(this.organisations.reduce((total, org) => total + org.consommation, 0));
}

get filteredSubscriptions(): Subscription[] {
  return this.subscriptions.filter(sub => {
    const matchesSearch = !this.searchTerm ? true :
      sub.companyName.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
      sub.contactEmail.toLowerCase().includes(this.searchTerm.toLowerCase());

    const matchesStatus = this.statusFilter === 'all' || sub.status === this.statusFilter;
    return matchesSearch && matchesStatus;
  });
   
}
      get paginatedSubscriptions(): Subscription[] {
    const startIndex = (this.currentPage - 1) * this.pageSize;
    const endIndex = startIndex + this.pageSize;
    return this.filteredSubscriptions.slice(startIndex, endIndex);
  }

  // Add pagination methods
  getPageNumbers(): number[] {
    const totalPages = Math.ceil(this.filteredSubscriptions.length / this.pageSize);
    if (totalPages <= 7) {
      return Array.from({ length: totalPages }, (_, i) => i + 1);
    }

    if (this.currentPage <= 4) {
      return [1, 2, 3, 4, 5, -1, totalPages];
    }

    if (this.currentPage >= totalPages - 3) {
      return [1, -1, totalPages - 4, totalPages - 3, totalPages - 2, totalPages - 1, totalPages];
    }

    return [
      1,
      -1,
      this.currentPage - 1,
      this.currentPage,
      this.currentPage + 1,
      -1,
      totalPages
    ];
  }

  onPageChange(page: number): void {
    this.currentPage = page;
  }

  previousPage(): void {
    if (this.currentPage > 1) {
      this.currentPage--;
    }
  }

  nextPage(): void {
    if (this.currentPage < Math.ceil(this.filteredSubscriptions.length / this.pageSize)) {
      this.currentPage++;
    }
  }
}
