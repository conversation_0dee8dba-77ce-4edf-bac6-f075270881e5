<div class="modal-overlay">
  <div class="modal-container">
    <div class="modal-color-bar"></div>
    <div class="modal-content">
      <div class="modal-header">
        <div class="modal-title-container">
          <div class="modal-icon">
            <mat-icon class="icon">warning</mat-icon>
          </div>
          <h3>Confirmer l'action</h3>
        </div>
        <button (click)="close.emit()" class="modal-close-button">
          <mat-icon class="icon">close</mat-icon>
        </button>
      </div>

      <div class="modal-body">
        <p>
          Êtes-vous sûr de vouloir <strong>{{ getActionLabel() }}</strong> l'abonnement de :
        </p>
        <div class="subscription-info">
          <div class="company-name">{{ subscription?.companyName }}</div>
          <div class="contact-email">{{ subscription?.contactEmail }}</div>
        </div>
      </div>

      <div class="modal-footer">
        <button (click)="close.emit()" class="cancel-button">
          Annuler
        </button>
        <button (click)="confirm.emit()" class="confirm-button" [ngClass]="getConfirmButtonColor()">
          Confirmer
        </button>
      </div>
    </div>
  </div>
</div>
