import { Component, EventEmitter, Input, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { CategoryStatCardComponent } from '../../../components/category-stat-card/category-stat-card.component';
import { trigger, transition, style, animate } from '@angular/animations';

@Component({
  selector: 'app-categories-sites',
  standalone: true,
  imports: [CommonModule, CategoryStatCardComponent],
  templateUrl: './categories-sites.component.html',
  styleUrls: ['./categories-sites.component.css'],
    animations: [
    trigger('cardAnimation', [
      transition(':enter', [
        style({ opacity: 0, transform: 'translateY(20px)' }),
        animate('300ms ease-out', style({ opacity: 1, transform: 'translateY(0)' }))
      ])
    ])
  ]
})
export class CategoriesSitesComponent {
  @Input() siteStats: {type: string, count: number}[] = [];
  @Output() typeSelected = new EventEmitter<string>();
  @Output() viewDetailsClicked = new EventEmitter<string>(); // Add this new EventEmitter

  constructor(readonly router: Router) {}

  onTypeClick(type: string) {
    this.typeSelected.emit(type);
  }

  onDetailsClick(type: string) {
    this.viewDetailsClicked.emit(type); // Emit the event to the parent
    // The navigation logic can remain here or be moved to the parent component
    this.router.navigate(['/organisation-details/1'], {
      queryParams: { type: type }
    });
  }
}