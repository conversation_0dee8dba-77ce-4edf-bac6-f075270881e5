import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';

export interface SensorDetails {
  id?: number;
  name: string;
  type: string;
  imageURL: string;
  measurementRange?: string;
  description?: string;
  fabricant?: string;
}

export interface SensorImages {
  images: string[];
}

@Injectable({
  providedIn: 'root'
})
export class SensorService {
  readonly apiUrl = 'http://************:5544/api/SensorDetails';

  constructor(readonly http: HttpClient) {}

  getSensors(): Observable<SensorDetails[]> {
    return this.http.get<SensorDetails[]>(this.apiUrl);
  }

  getSensor(id: number): Observable<SensorDetails> {
    return this.http.get<SensorDetails>(`${this.apiUrl}/${id}`);
  }

  createSensor(sensor: SensorDetails): Observable<SensorDetails> {
    return this.http.post<SensorDetails>(this.apiUrl, sensor);
  }

  updateSensor(id: number, sensor: SensorDetails): Observable<void> {
    return this.http.put<void>(`${this.apiUrl}/${id}`, sensor);
  }

  deleteSensor(id: number): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}/${id}`);
  }

  getImages(): Observable<SensorImages> {
    return this.http.get<SensorImages>(`${this.apiUrl}/images`);
  }
}