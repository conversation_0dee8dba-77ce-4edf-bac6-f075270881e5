export interface Position {
    x: number;
    y: number;
  }
  
  export enum DeviceType {
    LAMP = 'LAMP',
    CLIMATE = 'CLIMATE',
    MOTION_SENSOR = 'MOTION_SENSOR',
    TEMPERATURE_SENSOR = 'TEMPERATURE_SENSOR',
    CONTROLLER = 'CONTROLLER',
    CAMERA = 'CAMERA',
    DOOR_SENSOR = 'DOOR_SENSOR',
    WINDOW_SENSOR = 'WINDOW_SENSOR',
    OTHER = 'OTHER'
  }
  
  export enum DeviceStatus {
    RUNNING = 'RUNNING',
    PAUSED = 'PAUSED',
    PAIRING = 'PAIRING',
    STOPPED = 'STOPPED'
  }
  
  export interface Device {
    id: string;
    name: string;
    type: DeviceType;
    status: DeviceStatus;
    position: Position;
    room?: string;
    battery?: number;
    isZigbee?: boolean;
  }