/* devices.component.css - Updated version with French translations, grid/table views, and staggered grid */

/* Overall container */
.device-control-container {
  padding: 16px;
  max-width: 1800px;
  margin: 0 auto;
  font-family: 'Roboto', sans-serif;
  color: #374151;
  background-color: #f8f9fa; /* Light background for the container */
}

/* Header Section */
.header-card {
  background-color: #ffffff;
  border-radius: 12px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.08); /* More pronounced shadow */
  margin-bottom: 24px;
}

.header-card mat-card-header {
  padding: 16px;
  align-items: center;
}

.header-avatar {
  background-color: var(--green-main); /* Primary color */
  color: white;
  border-radius: 50%;
  width: 48px; /* Slightly larger avatar */
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
}

.header-avatar mat-icon {
  font-size: 28px; /* Larger icon */
}

.header-card mat-card-title {
  font-size: 22px; /* Larger title */
  font-weight: 700;
  color: #374151;
}

.header-card mat-card-subtitle {
  font-size: 14px;
  color: #616161;
  display: flex;
  align-items: center;
  gap: 8px;
}

.bridge-status {
  display: flex;
  align-items: center;
  gap: 4px;
}

.status-icon-online {
  color: #4caf50; /* Green for online */
}

.status-icon-offline {
  color: #f44336; /* Red for offline */
}

.status-online {
  color: #4caf50;
  font-weight: 500;
}

.status-offline {
  color: #f44336;
  font-weight: 500;
}

/* Statistics Grid */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
  gap: 20px;
  margin-bottom: 24px;
}

.stat-card {
  border-radius: 12px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.06);
  transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
  background-color: #ffffff;
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.12);
}

.stat-content {
  display: flex;
  align-items: center;
  padding: 20px;
}

.stat-icon {
  font-size: 36px; /* Larger stat icons */
  margin-right: 20px;
  border-radius: 50%;
  width: 60px; /* Larger circular background */
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.stat-icon.total { background-color: #e3f2fd; color: #1976d2; } /* Blue */
.stat-icon.online { background-color: #e8f5e9; color: #388e3c; } /* Green */
.stat-icon.lights { background-color: #fff3e0; color: #ff9800; } /* Orange */
.stat-icon.sensors { background-color: #ede7f6; color: #673ab7; } /* Deep Purple */

.stat-info {
  display: flex;
  flex-direction: column;
}

.stat-number {
  font-size: 28px; /* Larger stat numbers */
  font-weight: 700;
  line-height: 1.2;
  color: #374151;
}

.stat-label {
  font-size: 15px;
  color: #616161;
  margin-top: 4px;
}

/* Filter Section */
.filter-card {
  background-color: #ffffff;
  border-radius: 12px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.08);
  margin-bottom: 24px;
  padding: 16px;
}

.filter-section {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  padding-bottom: 16px; /* Add padding to separate from toggle/results */
  border-bottom: 1px solid #eee; /* Separator */
  margin-bottom: 16px;
}

.search-field, .filter-field {
  flex: 1;
  min-width: 200px; /* Slightly smaller min-width for better responsiveness */
  max-width: 300px; /* Max-width for search field */
}

.filter-field {
  min-width: 150px;
}

.clear-filters-btn {
  align-self: flex-end;
  margin-bottom: 21px; /* Adjust margin */
  height: 56px; /* Match form-field height */
}

.view-toggle-section {
  display: flex;
  justify-content: center; /* Center the toggle buttons */
  margin-bottom: 16px;
}

.mat-button-toggle-group {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
}

.mat-button-toggle {
  padding: 0 20px;
  font-weight: 500;
  color: #616161;
  background-color: #f0f2f5; /* Light background for unselected */
  transition: all 0.2s ease;
}

.mat-button-toggle-checked {
  background-color: var(--green-main) !important; /* Primary color for selected */
  color: white !important;
  box-shadow: 0 2px 5px rgba(63, 81, 181, 0.3);
}

.mat-button-toggle-checked .mat-icon {
  color: white !important;
}

.mat-button-toggle mat-icon {
  margin-right: 8px;
}

.filter-results {
  padding: 0 0 8px;
  font-size: 15px;
  color: #616161;
  text-align: center; /* Center results text */
}

/* Devices Grid (Staggered/Masonry Layout) */
.devices-grid {
  /* Use CSS Multi-column layout for staggered effect */
  column-count: 3; /* Adjust based on desired number of columns */
  column-gap: 20px;
  padding: 8px; /* Padding inside the grid container */
}

.device-card {
  break-inside: avoid; /* Prevents cards from breaking across columns */
  margin-bottom: 20px; /* Gap between cards in the same column */
  
  border-radius: 12px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.06);
  transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
  overflow: hidden;
  background-color: #ffffff;
  width: 100%; /* Important for column-count to work correctly */
  box-sizing: border-box; /* Include padding/border in width */
}

.device-card:hover {
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.12);
  transform: translateY(-4px);
}

.device-card mat-card-header {
  padding: 16px;
  border-bottom: 1px solid #e0e0e0;
}

.device-avatar {
  background-color: #e0e0e0; /* Neutral background */
  color: #616161;
  border-radius: 50%;
  width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
}

.device-avatar mat-icon {
  font-size: 24px;
}

.device-card mat-card-title {
  font-size: 18px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 4px;
}

.device-card mat-card-subtitle {
  font-size: 13px;
  color: #757575;
}

.device-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

/* Sensor Data & Controls */
.device-card mat-card-content {
  padding: 16px;
}

.sensor-data-grid {
  display: grid;
  grid-template-columns: 1fr; /* Stack items by default */
  gap: 16px;
  padding-bottom: 16px; /* Space before health summary */
}

.control-item, .sensor-item {
  padding: 8px 0;
  border-bottom: 1px dashed #f0f0f0; /* Dashed separator for better visual flow */
}

.sensor-data-grid > *:last-child {
  border-bottom: none; /* No border for the last item */
}

.control-toggle, .slider-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px; /* Space between header and slider */
}

.control-label, .sensor-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 15px;
  font-weight: 500;
  color: #555;
}

.control-value, .sensor-value {
  font-weight: 600;
  color: #374151;
  font-size: 15px;
}

.mat-slide-toggle {
  margin-right: 0; /* Remove default margin */
}

.mat-slide-toggle.mat-checked .mat-slide-toggle-thumb {
  background-color: #4caf50; /* Green thumb for ON */
}
.mat-slide-toggle.mat-checked .mat-slide-toggle-bar {
  background-color: #a5d6a7; /* Lighter green bar for ON */
}

mat-slider {
  width: 100%;
}

/* Colors for sensor icons */
.sensor-icon-temperature { color: #f44336; } /* Red */
.sensor-icon-humidity { color: #2196f3; }    /* Blue */
.sensor-icon-battery { color: #4caf50; }     /* Green */
.sensor-icon-linkquality { color: #9e9e9e; } /* Gray */
.sensor-icon-state { color: #4caf50; }       /* Green */
.sensor-icon-brightness { color: #ffc107; }  /* Amber */
.sensor-icon-occupancy { color: #673ab7; }   /* Deep Purple */
.sensor-icon-contact { color: #ff9800; }     /* Orange */
.sensor-icon-power { color: #607d8b; }       /* Blue Gray */

/* Boolean status colors */
.boolean-true, .boolean-ON { color: #4caf50; font-weight: bold; }
.boolean-false, .boolean-OFF { color: #f44336; font-weight: bold; }

/* Numeric value specifics */
.value-number {
  font-size: 1.1em;
}
.value-unit {
  font-size: 0.8em;
  color: #757575;
  margin-left: 4px;
}

mat-progress-bar {
  height: 8px;
  border-radius: 4px;
  margin-top: 8px;
}

/* Device Health Summary */
.device-health {
  padding-top: 16px;
  margin-top: 16px;
}

.mat-divider {
  margin-bottom: 16px;
  border-top-color: #e0e0e0;
}

.health-items {
  display: flex;
  justify-content: space-around;
  gap: 10px;
  flex-wrap: wrap;
}

.health-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
  color: #616161;
}

.health-item mat-icon {
  font-size: 18px;
}

.last-seen {
  color: #757575;
}

/* No Data Message */
.no-data {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 32px;
  color: #9e9e9e;
  font-size: 16px;
}

.no-data mat-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

/* Table View */
.devices-table {
  width: 100%;
  overflow-x: auto; /* Enable horizontal scrolling for small screens */
  margin-bottom: 24px;
}

.table-container {
  background-color: #ffffff;
  border-radius: 12px;
  overflow: hidden; /* Ensures border-radius is applied to content */
}

.mat-table {
  width: 100%;
  border-collapse: separate; /* Allows for rounded corners on cells if needed */
  border-spacing: 0;
}

.mat-header-cell {
  background-color: #f5f5f5; /* Light gray header */
  color: #374151;
  font-weight: 600;
  font-size: 14px;
  padding: 16px 8px;
  text-align: left;
}

.mat-cell {
  padding: 12px 8px;
  border-bottom: 1px solid #eeeeee;
  color: #424242;
  font-size: 14px;
  vertical-align: middle;
}

.mat-row:last-child .mat-cell {
  border-bottom: none;
}

.mat-row:hover {
  background-color: #fcfcfc;
}

.device-name-cell {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
}

.table-device-icon {
  font-size: 20px;
  color: #616161;
}

.status-online {
  color: #4caf50;
  font-weight: 500;
}
.status-offline {
  color: #f44336;
  font-weight: 500;
}

.table-controls {
  display: flex;
  flex-wrap: wrap; /* Allow controls to wrap */
  gap: 12px; /* Space between controls */
  align-items: center;
}

.table-controls mat-slide-toggle,
.table-controls mat-slider {
  margin: 0; /* Remove default margins */
}

/* No devices card */
.no-devices-card {
  background-color: #ffffff;
  border-radius: 12px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.08);
  margin-bottom: 24px;
  text-align: center;
  padding: 32px;
}

.no-devices-content .large-icon {
  font-size: 80px; /* Larger icon for emphasis */
  color: #bdbdbd;
  margin-bottom: 24px;
}

.no-devices-content h3 {
  font-size: 24px;
  color: #374151;
  margin-bottom: 12px;
}

.no-devices-content p {
  font-size: 16px;
  color: #757575;
  line-height: 1.5;
  max-width: 500px;
  margin: 0 auto 8px;
}

/* Responsive adjustments */
@media (max-width: 1200px) {
  .devices-grid {
    column-count: 2; /* 2 columns on medium screens */
  }
}

@media (max-width: 768px) {
  .filter-section {
    flex-direction: column;
    align-items: stretch;
  }
  .search-field, .filter-field, .clear-filters-btn {
    min-width: 100%; /* Full width on small screens */
    max-width: 100%;
  }
  
  .stats-grid {
    grid-template-columns: 1fr 1fr; /* 2 columns on small screens */
  }

  .devices-grid {
    column-count: 1; /* 1 column on small screens (no stagger) */
  }

  .mat-header-cell, .mat-cell {
    padding: 12px 6px; /* Slightly less padding on small screens */
  }
}

@media (max-width: 480px) {
  .stats-grid {
    grid-template-columns: 1fr; /* Stack stats cards on very small screens */
  }

  .header-card mat-card-title {
    font-size: 20px;
  }
  .header-card mat-card-subtitle {
    font-size: 13px;
  }
  .stat-icon {
    font-size: 32px;
    width: 52px;
    height: 52px;
  }
  .stat-number {
    font-size: 24px;
  }
  .stat-label {
    font-size: 13px;
  }
}