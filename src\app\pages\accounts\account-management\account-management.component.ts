import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { MatDialogModule, MatDialog } from '@angular/material/dialog';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { RouterLink } from '@angular/router';
import { EditEnterpriseDialogComponent } from '../../auth/edit-enterprise-dialog/edit-enterprise-dialog.component';
import { AuthService } from '../../../core/services/auth.service';
import { MatSnackBar } from '@angular/material/snack-bar';
import { trigger, transition, style, animate } from '@angular/animations';
import { MatFormFieldModule } from '@angular/material/form-field';
import { EditAdminDialogComponent } from '../../../pages/edit-admin-dialog/edit-admin-dialog.component';
import { TableModule } from 'primeng/table';
import { ButtonModule } from 'primeng/button';
import { TooltipModule } from 'primeng/tooltip';

interface EnterpriseUser {
  id: string;
  userName: string;
  email: string;
  fullName: string;
  enterpriseName: string;
  numberOfEmployees: number;
  contractDate: string;
  category: string;
}

interface AdminUser {
  id: string;
  userName: string;
  email: string;
  fullName: string;
}

@Component({
  selector: 'app-account-management',
  standalone: true,
  imports: [
    CommonModule,
    MatButtonModule,
    MatCardModule,
    MatIconModule,
    MatDialogModule,
    MatProgressSpinnerModule,
    MatFormFieldModule,
    RouterLink,
    TableModule,
    ButtonModule,
    TooltipModule
  ],
  animations: [
    trigger('tableAnimation', [
      transition(':enter', [
        style({ transform: 'translateY(20px)', opacity: 0 }),
        animate('500ms cubic-bezier(0.35, 0, 0.25, 1)', 
          style({ transform: 'translateY(0)', opacity: 1 }))
      ])
    ])
  ],
  templateUrl: './account-management.component.html',
  styleUrls: ['./account-management.component.css']
})
export class AccountManagementComponent implements OnInit {
  users: EnterpriseUser[] = [];
  adminUsers: AdminUser[] = [];
  isAdmin = false;
  isSuperAdmin = false;
  isLoading = false;

  // Pagination for enterprise users
  enterpriseFirst: number = 0;
  enterpriseRows: number = 5;

  // Pagination for admin users
  adminFirst: number = 0;
  adminRows: number = 5;

  constructor(
    public dialog: MatDialog, // Changed to public
    readonly authService: AuthService,
    readonly snackBar: MatSnackBar
  ) {}

  readonly EditAdminDialogComponent = EditAdminDialogComponent;
  readonly EditEnterpriseDialogComponent = EditEnterpriseDialogComponent;

  ngOnInit(): void {
    const roles = this.authService.getRoles();
    this.isAdmin = roles.includes('Admin');
    this.isSuperAdmin = roles.includes('SuperAdmin');
    this.loadUsers();
    if (this.isSuperAdmin) {
      this.loadAdminUsers();
    }
  }

  loadAdminUsers(): void {
    this.authService.getAdminUsers().subscribe({
      next: (admins) => {
        this.adminUsers = admins;
      },
      error: (error) => {
        console.error('Error loading admin users:', error);
        this.showMessage('Failed to load admin users');
      }
    });
  }

  loadUsers(): void {
    this.authService.getEnterpriseUsers().subscribe({
      next: (users) => {
        this.users = users;
      },
      error: (error) => {
        console.error('Error loading users:', error);
        this.showMessage('Failed to load users');
      }
    });
  }

  deleteAdmin(userId: string): void {
    if (confirm('Are you sure you want to delete this admin?')) {
      this.authService.deleteAdminUser(userId).subscribe({
        next: () => {
          this.loadAdminUsers();
          this.showMessage('Admin deleted successfully');
        },
        error: (error) => {
          console.error('Error deleting admin:', error);
          this.showMessage('Failed to delete admin');
        }
      });
    }
  }

  openEditDialog(user: EnterpriseUser): void {
    const dialogRef = this.dialog.open(this.EditEnterpriseDialogComponent, {
      width: '500px',
      data: { ...user }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.authService.updateEnterpriseUser(user.id, result).subscribe({
          next: () => {
            this.loadUsers();
            this.showMessage('User updated successfully');
          },
          error: (error) => {
            console.error('Error updating user:', error);
            this.showMessage('Failed to update user');
          }
        });
      }
    });
  }

  openEditAdminDialog(admin: AdminUser): void {
    const dialogRef = this.dialog.open(this.EditAdminDialogComponent, {
      width: '500px',
      data: { ...admin }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.authService.updateAdminUser(admin.id, result).subscribe({
          next: () => {
            this.loadAdminUsers();
            this.showMessage('Admin updated successfully');
          },
          error: (error) => {
            console.error('Error updating admin:', error);
            this.showMessage('Failed to update admin');
          }
        });
      }
    });
  }

  deleteUser(userId: string): void {
    if (confirm('Are you sure you want to delete this user?')) {
      this.authService.deleteEnterpriseUser(userId).subscribe({
        next: () => {
          this.loadUsers();
          this.showMessage('User deleted successfully');
        },
        error: (error) => {
          console.error('Error deleting user:', error);
          this.showMessage('Failed to delete user');
        }
      });
    }
  }

  private showMessage(message: string): void {
    this.snackBar.open(message, 'Close', {
      duration: 3000,
      horizontalPosition: 'end',
      verticalPosition: 'top'
    });
  }

  // Pagination for enterprise users
  enterpriseNext(): void {
    this.enterpriseFirst = this.enterpriseFirst + this.enterpriseRows;
  }

  enterprisePrev(): void {
    this.enterpriseFirst = this.enterpriseFirst - this.enterpriseRows;
  }

  enterpriseReset(): void {
    this.enterpriseFirst = 0;
  }

  isEnterpriseFirstPage(): boolean {
    return this.enterpriseFirst === 0;
  }

  isEnterpriseLastPage(): boolean {
    return this.enterpriseFirst >= this.users.length - this.enterpriseRows;
  }

  enterprisePageChange(event: any): void {
    this.enterpriseFirst = event.first;
    this.enterpriseRows = event.rows;
  }

  // Pagination for admin users
  adminNext(): void {
    this.adminFirst = this.adminFirst + this.adminRows;
  }

  adminPrev(): void {
    this.adminFirst = this.adminFirst - this.adminRows;
  }

  adminReset(): void {
    this.adminFirst = 0;
  }

  isAdminFirstPage(): boolean {
    return this.adminFirst === 0;
  }

  isAdminLastPage(): boolean {
    return this.adminFirst >= this.adminUsers.length - this.adminRows;
  }

  adminPageChange(event: any): void {
    this.adminFirst = event.first;
    this.adminRows = event.rows;
  }
}