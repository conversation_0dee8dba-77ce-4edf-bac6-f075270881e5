import { Component, Input, Output, EventEmitter } from '@angular/core';
import { OrganisationService,Organisation } from '../../core/services/organisation.service';
import { trigger, state, style, transition, animate } from '@angular/animations';
import { CommonModule } from '@angular/common';


@Component({
  selector: 'app-card-organisation',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './card-organisation.component.html',
  styleUrls: ['./card-organisation.component.css'],
  animations: [
    trigger('cardHover', [
      state('initial', style({ transform: 'scale(1)', boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)' })),
      state('hovered', style({ transform: 'scale(1.02)', boxShadow: '0 8px 20px rgba(0, 0, 0, 0.15)' })),
      transition('initial <=> hovered', animate('0.2s ease-in-out'))
    ])
  ]
})
export class CardOrganisationComponent {
  @Input() organisation!: Organisation;
  @Output() viewDetails = new EventEmitter<number>();
  @Output() editOrg = new EventEmitter<number>();
  @Output() deleteOrg = new EventEmitter<number>();

  cardState = 'initial';
   imageUrl: string | undefined;

  constructor(public organisationService: OrganisationService) {}
    ngOnInit() {
        this.loadOrganisationImage();
    }
        private loadOrganisationImage() {
        if (this.organisation.id) {
            this.organisationService.getOrganisationImage(this.organisation.id)
                .subscribe(blob => {
                    const reader = new FileReader();
                    reader.onloadend = () => {
                        this.imageUrl = reader.result as string;
                    };
                    reader.readAsDataURL(blob);
                });
        }
    }
  onMouseEnter() { this.cardState = 'hovered'; }
  onMouseLeave() { this.cardState = 'initial'; }
  onDetailsClick() { this.viewDetails.emit(this.organisation.id); }
  onEditClick(event: Event) { event.stopPropagation(); this.editOrg.emit(this.organisation.id); }
  onDeleteClick(event: Event) { event.stopPropagation(); this.deleteOrg.emit(this.organisation.id); }

  getImageUrl() { return this.organisationService.getImageUrl(this.organisation.logoPath ?? ''); }
}