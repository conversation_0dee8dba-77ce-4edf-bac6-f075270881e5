<div class="dashboard-container">
  <!-- Header -->
  <div class="dashboard-header">
    <div class="header-content">
      <div class="header-left">
        <div class="header-icon">
          <mat-icon class="icon">groups</mat-icon>
        </div>
        <div>
          <h1>Administration des Abonnements</h1>
          <p>Gestion centralisée des comptes clients</p>
        </div>
      </div>
      <div class="header-right">
        <p>Total abonnements</p>
        <p>{{ subscriptions.length }}</p>
      </div>
    </div>
  </div>

  <div class="dashboard-content">
    <!-- Stats Grid -->
        <div class="stats-grid">
          <app-stat-card
            [icon]="'business'"
            title="Organisations"
            [value]="organisations.length"
            subtitle="total"
            color="stat-grey"
          ></app-stat-card>
          
          <app-stat-card
            [icon]="'location_on'"
            title="Sites"
            [value]="getTotalSites()"
            subtitle="total"
            color="stat-green"
          ></app-stat-card>
          
          <app-stat-card
            [icon]="'devices'"
            title="Équipements"
            [value]="getTotalEquipments()"
            subtitle="total"
            color="stat-yellow"
          ></app-stat-card>
          
          <app-stat-card
            [icon]="'bolt'"
            title="Consommation"
            [value]="getTotalConsumption()"
            subtitle="kWh"
            color="stat-red"
          ></app-stat-card>
        </div>

    <!-- Filters and Search -->
    <div class="filter-card">
      <div class="filter-content">
        <div class="filter-row">
          <div class="search-container">
            <mat-icon class="search-icon">search</mat-icon>
            <input
              type="text"
              placeholder="Rechercher par entreprise, email ou contact..."
              [(ngModel)]="searchTerm"
              class="search-input"
            />
          </div>
          
          <div class="filter-controls">
            <mat-icon class="filter-icon">tune</mat-icon>
            <select
              [(ngModel)]="statusFilter"
              class="status-select"
            >
              <option *ngFor="let option of statusOptions" [value]="option.value">
                {{ option.label }} ({{ option.count }})
              </option>
            </select>
          </div>
        </div>
      </div>
    </div>

<!-- Replace the subscription table section with this -->
<div class="table-section">
  <!-- Subscriptions Table -->
  <app-subscription-table 
    [subscriptions]="paginatedSubscriptions"
    [getAvailableActions]="getAvailableActions.bind(this)"
    (action)="handleAction($event.subscription, $event.action)"
  ></app-subscription-table>

  <!-- Pagination Controls -->
  <div class="pagination-controls">
    <button 
      class="pagination-button"
      [disabled]="currentPage === 1"
      (click)="previousPage()">
      <i class="material-icons">3</i>
    </button>

    <div class="page-numbers">
      <button 
        *ngFor="let page of getPageNumbers()"
        class="page-number-button"
        [class.active]="page === currentPage"
        [class.separator]="page === -1"
        [disabled]="page === -1"
        (click)="page !== -1 && onPageChange(page)">
        {{ page === -1 ? '...' : page }}
      </button>
    </div>

    <button 
      class="pagination-button"
      [disabled]="currentPage === Math.ceil(filteredSubscriptions.length / pageSize)"
      (click)="nextPage()">
      <i class="material-icons">chevron_right</i>
    </button>

    <div class="pagination-info">
      {{ pageSize }} / page
    </div>
  </div>
</div>
  </div>

  <!-- Action Confirmation Modal -->
  <app-action-modal 
    *ngIf="showActionModal && selectedSubscription"
    [subscription]="selectedSubscription"
    [actionType]="actionType"
    (close)="showActionModal = false"
    (confirm)="confirmAction()"
  ></app-action-modal>
</div>
