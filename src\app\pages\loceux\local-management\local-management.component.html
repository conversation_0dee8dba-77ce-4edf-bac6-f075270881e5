<div class="local-management-container">
  <div class="header-section">
    <div class="page-title">
      <h1 class="title">
        <i class="material-icons title-icon">meeting_room</i> Gestion des Locaux
      </h1>
    </div>

    <div class="actions">
      <button class="create-button" (click)="showAddLocalForm()" *ngIf="!showCreateForm">
        <i class="material-icons action-icon">add</i> Créer Local
      </button>
    </div>
  </div>

  <div class="create-form-card" *ngIf="showCreateForm" [@fadeIn]>
    <form [formGroup]="createLocalForm" (ngSubmit)="submitCreateForm()">
      <div class="form-grid">
        <div class="form-group">
          <label for="type">Type</label>
          <select id="type" formControlName="type" required>
            <option *ngFor="let type of localTypes" [value]="type">
              {{type}}
            </option>
          </select>
        </div>

        <div class="form-group">
          <label for="etage">Étage</label>
          <input id="etage" type="number" formControlName="etage">
        </div>

        <div class="form-group">
          <label for="surface">Surface (m²)</label>
          <input id="surface" type="number" formControlName="surface">
        </div>

        <div class="form-group">
          <label for="hauteurSousPlafond">Hauteur sous plafond (m)</label>
          <input id="hauteurSousPlafond" type="number" formControlName="hauteurSousPlafond">
        </div>

        <div class="form-group">
          <label for="capacitePersonnes">Capacité (personnes)</label>
          <input id="capacitePersonnes" type="number" formControlName="capacitePersonnes">
        </div>

        <div class="form-group">
          <label for="description">Description</label>
          <textarea id="description" formControlName="description"></textarea>
        </div>

        <div class="form-group">
          <label for="temperatureCible">Température cible (°C)</label>
          <input id="temperatureCible" type="number" formControlName="temperatureCible">
        </div>

        <div class="form-group">
          <label for="consommationElectriqueMensuelle">Consommation électrique mensuelle (kWh)</label>
          <input id="consommationElectriqueMensuelle" type="number" formControlName="consommationElectriqueMensuelle">
        </div>

        <div class="form-group">
          <label for="dateDerniereMaintenance">Date dernière maintenance</label>
          <input id="dateDerniereMaintenance" type="date" formControlName="dateDerniereMaintenance">
        </div>

        <div class="form-group">
          <label for="imageArchitecture2D">Plan 2D</label>
          <input type="file" id="imageArchitecture2D" (change)="onArchitecture2DSelected($event)" accept="image/*">
        </div>

        <div class="form-group">
          <label for="imageLocale">Photo du local</label>
          <input type="file" id="imageLocale" (change)="onLocaleImageSelected($event)" accept="image/*">
        </div>
      </div>

      <div class="form-actions">
        <button type="button" (click)="hideAddLocalForm()">Annuler</button>
        <button type="submit" [disabled]="!createLocalForm.valid">Créer</button>
      </div>
    </form>
  </div>

    <div class="edit-form-card" *ngIf="showEditForm && selectedLocal" [@fadeIn]>
  <h2 class="form-title">Modifier le Local</h2>
  <form [formGroup]="editLocalForm" (ngSubmit)="submitEditForm()">
    <div class="form-grid">
      <div class="form-group">
        <label for="edit-type">Type</label>
        <select id="edit-type" formControlName="type" required>
          <option *ngFor="let type of localTypes" [value]="type">
            {{type}}
          </option>
        </select>
      </div>

      <div class="form-group">
        <label for="edit-etage">Étage</label>
        <input id="edit-etage" type="number" formControlName="etage">
      </div>

      <div class="form-group">
        <label for="edit-surface">Surface (m²)</label>
        <input id="edit-surface" type="number" formControlName="surface">
      </div>

      <div class="form-group">
        <label for="edit-hauteurSousPlafond">Hauteur sous plafond (m)</label>
        <input id="edit-hauteurSousPlafond" type="number" formControlName="hauteurSousPlafond">
      </div>

      <div class="form-group">
        <label for="edit-capacitePersonnes">Capacité (personnes)</label>
        <input id="edit-capacitePersonnes" type="number" formControlName="capacitePersonnes">
      </div>

      <div class="form-group">
        <label for="edit-description">Description</label>
        <textarea id="edit-description" formControlName="description"></textarea>
      </div>

      <div class="form-group">
        <label for="edit-temperatureCible">Température cible (°C)</label>
        <input id="edit-temperatureCible" type="number" formControlName="temperatureCible">
      </div>

      <div class="form-group">
        <label for="edit-consommationElectriqueMensuelle">Consommation électrique mensuelle (kWh)</label>
        <input id="edit-consommationElectriqueMensuelle" type="number" formControlName="consommationElectriqueMensuelle">
      </div>

      <div class="form-group">
        <label for="edit-dateDerniereMaintenance">Date dernière maintenance</label>
        <input id="edit-dateDerniereMaintenance" type="date" formControlName="dateDerniereMaintenance">
      </div>

      <div class="form-group">
        <label for="edit-imageArchitecture2D">Plan 2D</label>
        <input type="file" id="edit-imageArchitecture2D" (change)="onArchitecture2DSelected($event)" accept="image/*">
      </div>

      <div class="form-group">
        <label for="edit-imageLocale">Photo du local</label>
        <input type="file" id="edit-imageLocale" (change)="onLocaleImageSelected($event)" accept="image/*">
      </div>
    </div>

    <div class="form-actions">
      <button type="button" (click)="hideEditForm()">Annuler</button>
      <button type="submit" [disabled]="!editLocalForm.valid">Enregistrer</button>
    </div>
  </form>
</div>

  <div class="search-bar">
    <input 
      type="text" 
      [(ngModel)]="searchTerm" 
      (keyup)="filterLocals()"
      placeholder="Rechercher un local">
  </div>

  <div class="loading-spinner" *ngIf="isLoading">
    Chargement...
  </div>

  <div class="table-container" *ngIf="!isLoading">
    <app-generic-table
      [headers]="['Type', 'Étage', 'Surface', 'Capacité']"
      [keys]="['type', 'etage', 'surface', 'capacitePersonnes']"
      [data]="filteredLocals"
      [actions]="['view', 'edit', 'delete']"
      [pageSize]="pageSize"
      [currentPage]="currentPage - 1"
      [totalCount]="totalFilteredLocalsCount"
      (pageChange)="onPageChange($event)"
      (actionTriggered)="handleTableAction($event)"
    ></app-generic-table>
  </div>
</div>