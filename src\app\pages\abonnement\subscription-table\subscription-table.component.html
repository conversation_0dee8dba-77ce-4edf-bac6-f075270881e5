<div class="subscription-table-container">
  <div class="table-color-bar"></div>
  <div class="table-wrapper">
    <table class="subscription-table">
      <thead>
        <tr>
          <th>Entreprise</th>
          <th>Contact</th>
          <th>Abonnement</th>
          <th>Statut</th>
          <th>Données</th>
          <th>Dernière activité</th>
          <th class="text-center">Actions</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let subscription of subscriptions" class="table-row">
          <td>
            <div>
              <div class="company-name">{{ subscription.companyName }}</div>
              <div class="plan-name">Plan {{ subscription.plan }}</div>
            </div>
          </td>
          <td>
            <div>
              <div class="contact-name">{{ subscription.contactName }}</div>
              <div class="contact-email">
                <mat-icon class="icon">mail</mat-icon>
                {{ subscription.contactEmail }}
              </div>
            </div>
          </td>
          <td>
            <div class="subscription-date">
              <mat-icon class="icon">calendar_today</mat-icon>
              {{ formatDate(subscription.startDate) }}
            </div>
          </td>
          <td>
            <div class="status-badge" [ngClass]="getStatusConfig(subscription.status).color">
              <mat-icon class="icon">{{ getStatusConfig(subscription.status).icon }}</mat-icon>
              {{ getStatusConfig(subscription.status).label }}
            </div>
          </td>
          <td>
            <div class="data-container">
              <div class="data-item">
                <mat-icon class="icon">location_city</mat-icon>
                {{ subscription.sites }} sites
              </div>
              <div class="data-item">
                <mat-icon class="icon">bolt</mat-icon>
                {{ subscription.consumption.toLocaleString() }} kWh
              </div>
            </div>
          </td>
          <td>
            <div class="last-activity">
              {{ formatDate(subscription.lastActivity) }}
            </div>
          </td>
          <td class="text-center">
            <div class="action-menu">
              <details class="dropdown">
                <summary class="dropdown-trigger">
                  <mat-icon class="icon">more_vert</mat-icon>
                </summary>
                <div class="dropdown-menu">
                  <button 
                    *ngFor="let action of getAvailableActions(subscription.status)"
                    (click)="emitAction(subscription, action.type)"
                    class="dropdown-item"
                    [ngClass]="action.color"
                  >
                    <mat-icon class="icon">{{ action.icon }}</mat-icon>
                    {{ action.label }}
                  </button>
                </div>
              </details>
            </div>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</div>
