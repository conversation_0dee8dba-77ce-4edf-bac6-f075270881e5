/* Base styles */
.dashboard-container {
  min-height: 100vh;
  background-color: #f9fafb;
  font-family: 'Lato', sans-serif;
}

h1, h2, h3, h4, h5, h6 {
  font-family: 'Montserrat', sans-serif;
}

/* Header styles */
.dashboard-header {
  background-color: white;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  border-bottom: 1px solid #e5e7eb;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 1rem 1.5rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.header-icon {
  width: 2.5rem;
  height: 2.5rem;
  background: linear-gradient(to bottom right, #34d399, #059669);
  border-radius: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.header-icon .icon {
  width: 1.5rem;
  height: 1.5rem;
  color: white;
}

.header-left h1 {
  font-size: 1.5rem;
  font-weight: 700;
  color: #111827;
  margin: 0;
}

.header-left p {
  color: #4b5563;
  margin: 0.25rem 0 0;
  font-size: 0.875rem;
}

.header-right p:first-child {
  color: #4b5563;
  font-size: 0.875rem;
  margin: 0;
}

.header-right p:last-child {
  font-weight: 600;
  color: #111827;
  font-size: 1.25rem;
  margin: 0;
}

/* Dashboard content */
.dashboard-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem 1.5rem;
}

/* Stats grid */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(1, 1fr);
  gap: 1.5rem;
  margin-bottom: 2rem;
}

@media (min-width: 768px) {
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .stats-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

/* Filter card */
.filter-card {
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  border: 1px solid #e5e7eb;
  overflow: hidden;
  margin-bottom: 1.5rem;
}

.filter-card::before {
  content: '';
  display: block;
  height: 0.5rem;
  background-color: #10b981;
}

.filter-content {
  padding: 1.5rem;
}

.filter-row {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

@media (min-width: 768px) {
  .filter-row {
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
  }
}

.search-container {
  flex: 1;
  position: relative;
}

.search-icon {
  position: absolute;
  left: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  color: #9ca3af;
  width: 1.25rem;
  height: 1.25rem;
}

.search-input {
  width: 100%;
  padding: 0.75rem 0.75rem 0.75rem 2.5rem;
  border: 1px solid #d1d5db;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  transition: all 0.2s;
}

.search-input:focus {
  outline: none;
  border-color: #10b981;
  box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.2);
}

.filter-controls {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.filter-icon {
  color: #9ca3af;
  width: 1.25rem;
  height: 1.25rem;
}

.status-select {
  padding: 0.75rem 1rem;
  border: 1px solid #d1d5db;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  transition: all 0.2s;
}

.status-select:focus {
  outline: none;
  border-color: #10b981;
  box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.2);
}

/* Status badges */
.status-active {
  background-color: #d1fae5;
  color: #065f46;
  border-color: #a7f3d0;
}

.status-paused {
  background-color: #fef3c7;
  color: #92400e;
  border-color: #fde68a;
}

.status-inactive {
  background-color: #f3f4f6;
  color: #374151;
  border-color: #e5e7eb;
}

.status-banned {
  background-color: #fee2e2;
  color: #991b1b;
  border-color: #fca5a5;
}

/* Action colors */
.action-green {
  color: #059669;
}

.action-yellow {
  color: #d97706;
}

.action-gray {
  color: #4b5563;
}

.action-red {
  color: #dc2626;
}

/* Stat card colors */
.stat-green {
  background-color: #10b981;
}

.stat-yellow {
  background-color: #f59e0b;
}

.stat-gray {
  background-color: #6b7280;
}

.stat-red {
  background-color: #ef4444;
}


.icon, .search-icon, .filter-icon {
  width: 24px;
  height: 24px;
  /* Add other styles as needed */
}


/* Add these styles */
.table-section {
  background: white;
  border-radius: 0.5rem;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  margin-bottom: 2rem;
}

.pagination-controls {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem;
  border-top: 1px solid #e5e7eb;
}

.page-numbers {
  display: flex;
  gap: 0.25rem;
  align-items: center;
}

.page-number-button {
  min-width: 32px;
  height: 32px;
  border-radius: 4px;
  border: 1px solid #e2e8f0;
  background: white;
  color: #4a5568;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.875rem;
}

.page-number-button:hover:not(:disabled):not(.active) {
  background: #f7fafc;
  border-color: #cbd5e0;
}

.page-number-button.active {
  background: #10b981;
  color: white;
  border-color: #10b981;
}

.page-number-button.separator {
  border: none;
  background: transparent;
  cursor: default;
}

.page-number-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pagination-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 4px;
  border: 1px solid #e2e8f0;
  background: white;
  color: #4a5568;
  cursor: pointer;
  transition: all 0.2s ease;
}

.pagination-button:hover:not(:disabled) {
  background: #f7fafc;
  border-color: #cbd5e0;
}

.pagination-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pagination-info {
  margin-left: 1rem;
  color: #718096;
  font-size: 0.875rem;
}