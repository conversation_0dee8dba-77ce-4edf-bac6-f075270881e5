.subscription-table-container {
  background-color: white;
  border-radius: 0.75rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
  overflow: hidden;
}

.table-color-bar {
  height: 0.5rem;
  background: linear-gradient(to right, #059669, #34d399);
}

.table-wrapper {
  overflow-x: auto;
}

.subscription-table {
  width: 100%;
  border-collapse: collapse;
}

.subscription-table th {
  padding: 1rem 1.5rem;
  text-align: left;
  font-size: 0.875rem;
  font-weight: 600;
  color: #111827;
  background-color: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
  font-family: 'Montserrat', sans-serif;
}

.subscription-table td {
  padding: 1rem 1.5rem;
  font-size: 0.875rem;
  color: #4b5563;
  border-bottom: 1px solid #e5e7eb;
  font-family: 'Lato', sans-serif;
  vertical-align: middle;
}

.subscription-table tr:last-child td {
  border-bottom: none;
}

.subscription-table tr:hover {
  background-color: #f9fafb;
}

.text-center {
  text-align: center;
}

.company-name {
  font-weight: 600;
  color: #111827;
  font-family: '<PERSON><PERSON><PERSON>', sans-serif;
  margin-bottom: 0.25rem;
}

.plan-name {
  font-size: 0.75rem;
  color: #6b7280;
}

.contact-name {
  font-weight: 500;
  color: #111827;
  font-family: 'Montserrat', sans-serif;
  margin-bottom: 0.25rem;
}

.contact-email {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.75rem;
  color: #6b7280;
}

.subscription-date {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #6b7280;
}

/* Status Badge Styles */
.status-badge {
  display: inline-flex;
  align-items: center;
  padding: 0.375rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.813rem;
  font-weight: 500;
  gap: 0.375rem;
  font-family: 'Montserrat', sans-serif;
}

.status-active {
  background-color: #dcfce7;
  color: #166534;
}

.status-paused {
  background-color: #fef3c7;
  color: #92400e;
}

.status-inactive {
  background-color: #f3f4f6;
  color: #4b5563;
}

.status-banned {
  background-color: #fee2e2;
  color: #991b1b;
}

/* Data Items Styles */
.data-container {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.data-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.813rem;
  color: #6b7280;
}

.last-activity {
  font-size: 0.813rem;
  color: #6b7280;
}

/* Icon Styles */
.icon {
  display: flex !important;
  align-items: center;
  justify-content: center;
  width: 20px !important;
  height: 20px !important;
  font-size: 20px !important;
  line-height: 1 !important;
}

.contact-email .icon,
.subscription-date .icon,
.data-item .icon {
  color: #9ca3af;
  font-size: 16px !important;
  width: 16px !important;
  height: 16px !important;
}

.status-badge .icon {
  font-size: 16px !important;
  width: 16px !important;
  height: 16px !important;
}

/* Dropdown Menu Styles */
.action-menu {
  display: inline-block;
  position: relative;
}

.dropdown {
  position: relative;
}

.dropdown-trigger {
  list-style: none;
  padding: 0.5rem;
  border-radius: 0.5rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s ease;
}

.dropdown-trigger:hover {
  background-color: #f3f4f6;
}

.dropdown-trigger .icon {
  color: #6b7280;
}

.dropdown-menu {
  position: absolute;
  right: 0;
  margin-top: 0.5rem;
  min-width: 12rem;
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  border: 1px solid #e5e7eb;
  z-index: 50;
  overflow: hidden;
}

.dropdown-item {
  width: 100%;
  padding: 0.75rem 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  background: none;
  border: none;
  cursor: pointer;
  transition: background-color 0.2s ease;
  color: #374151;
  font-family: 'Lato', sans-serif;
}

.dropdown-item:hover {
  background-color: #f9fafb;
}

.dropdown-item .icon {
  font-size: 18px !important;
  width: 18px !important;
  height: 18px !important;
}

/* Action Colors */
.text-emerald-600 { color: #059669; }
.text-yellow-600 { color: #d97706; }
.text-gray-600 { color: #4b5563; }
.text-red-600 { color: #dc2626; }

/* Responsive adjustments */
@media (max-width: 1024px) {
  .subscription-table td,
  .subscription-table th {
    padding: 0.75rem 1rem;
  }
  
  .icon {
    font-size: 18px !important;
    width: 18px !important;
    height: 18px !important;
  }
}