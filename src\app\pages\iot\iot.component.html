<div class="container">
  <div class="header">
    <h1 class="page-title">
      <mat-icon class="title-icon">devices</mat-icon>
      Gestion des Capteurs IoT
    </h1>
    <button mat-flat-button class="add-button" (click)="openAddSensorDialog()">
      <mat-icon>add</mat-icon>
      <span>Ajouter</span>
    </button>
  </div>

  <p-table
  [value]="sensors"
  [paginator]="true"
  [rows]="rows"
  [first]="first"
  [showCurrentPageReport]="true"
  [tableStyle]="{'min-width': '50rem'}"
  currentPageReportTemplate="Affichage de {first} à {last} sur {totalRecords} capteurs"
  (onPage)="pageChange($event)"
  styleClass="sensor-table"
>
    <ng-template pTemplate="header">
      <tr>
        <th class="image-column">Image</th>
        <th>Nom</th>
        <th>Type</th>
        <th>Fabricant</th>
        <th>Plage de Mesure</th>
        <th>Description</th>
        <th class="actions-column">Actions</th>
      </tr>
    </ng-template>

    <ng-template pTemplate="body" let-sensor>
      <tr class="sensor-row">
        <td class="image-cell">
          <img [src]="sensor.imageURL" alt="Sensor Image" class="sensor-image">
        </td>
        <td>{{ sensor.name }}</td>
        <td>{{ sensor.type }}</td>
        <td>{{ sensor.fabricant || 'N/A' }}</td>
        <td>{{ sensor.measurementRange || 'N/A' }}</td>
        <td>{{ sensor.description || 'N/A' }}</td>
        <td class="actions-cell">
          <button mat-icon-button class="action-edit" matTooltip="Modifier" (click)="openEditSensorDialog(sensor)">
            <mat-icon>edit</mat-icon>
          </button>
          <button mat-icon-button class="action-delete" matTooltip="Supprimer" (click)="confirmDeleteSensor(sensor)">
            <mat-icon>delete</mat-icon>
          </button>
        </td>
      </tr>
    </ng-template>
  </p-table>

  <div class="no-data" *ngIf="sensors.length === 0">
    <mat-icon class="no-data-icon">sentiment_dissatisfied</mat-icon>
    <p>Aucun capteur trouvé</p>
  </div>
</div>