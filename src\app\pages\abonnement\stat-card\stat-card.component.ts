import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatIconModule } from '@angular/material/icon';

@Component({
  selector: 'app-stat-card',
  templateUrl: './stat-card.component.html',
  styleUrls: ['./stat-card.component.css'],
  standalone: true,
  imports: [CommonModule, MatIconModule]
})
export class StatCardComponent {
  @Input() icon: string = '';
  @Input() title: string = '';
  @Input() value: number = 0;
  @Input() subtitle: string = '';
  @Input() color: string = '';
  @Input() bgColor: string = '';

  getStatusColor(): string {
    const colors: { [key: string]: string } = {
      'stat-green': '#10b981',
      'stat-yellow': '#f59e0b',
      'stat-gray': '#6b7280',
      'stat-red': '#ef4444'
    };
    return colors[this.color] || colors['stat-gray'];
  }
}