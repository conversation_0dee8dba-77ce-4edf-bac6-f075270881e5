import { Injectable } from '@angular/core';
import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';

@Injectable({
  providedIn: 'root'
})
export class SensorDataService {
  private readonly apiUrl = 'http://localhost:5260/api/SensorData'; // Remplacez par l'URL de votre API

  constructor(readonly http: HttpClient) {}

  /**
   * Récupère la liste des tables disponibles.
   * @returns Observable contenant la liste des tables.
   */
  getAvailableSensorTables(): Observable<string[]> {
    return this.http.get<string[]>(`${this.apiUrl}/tables`).pipe(
      catchError(this.handleError)
    );
  }

  /**
   * Récupère les données d'une table spécifique.
   * @param tableName Nom de la table.
   * @returns Observable contenant les données de la table.
   */
  getSensorData(tableName: string): Observable<any[]> {
    return this.http.get<any[]>(`${this.apiUrl}/${tableName}`).pipe(
      catchError(this.handleError)
    );
  }

  /**
   * Gestion des erreurs HTTP.
   * @param error Erreur HTTP.
   * @returns Observable avec un message d'erreur.
   */
  private handleError(error: HttpErrorResponse): Observable<never> {
    let errorMessage = 'An unknown error occurred!';
    if (error.error instanceof ErrorEvent) {
      // Erreur côté client
      errorMessage = `Client-side error: ${error.error.message}`;
    } else {
      // Erreur côté serveur
      errorMessage = `Server-side error: ${error.status} - ${error.message}`;
    }
    console.error(errorMessage);
    return throwError(() => new Error(errorMessage));
  }
}