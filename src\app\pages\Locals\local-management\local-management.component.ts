/*import { Compo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  FormsModule,
  ReactiveFormsModule,
  FormGroup,
  FormControl,
  Validators,
} from '@angular/forms';
import { Local } from '../../../core/models/local.model';
import { CardLocalComponent } from '../../../components/card-local/card-local.component';
import { EditLocalComponent } from '../edit-local/edit-local.component';
import { LocalService } from '../../../core/services/local.service';
import { ActivatedRoute, Router } from '@angular/router';
import { MqttService, IMqttMessage } from 'ngx-mqtt';
import { Subscription } from 'rxjs';
import { SiteService } from '@app/core/services/sites.service';
import { Site } from '@app/core/services/organisation.service';

interface LocalUpdateEvent {
  updatedLocal: Local;
  formData: FormData;
}
interface LocalFormValues {
  siteId: number | null;
  type: string | null;
  etage: number | null;
  surface: number | null;
  hauteurSousPlafond: number | null;
  capacitePersonnes: number | null;
  description: string | null;
  temperatureCible: number | null;
  consommationElectriqueMensuelle: number | null;
  dateDerniereMaintenance: string | null;
}
@Component({
  selector: 'app-local-management',
  templateUrl: './local-management.component.html',
  styleUrls: ['./local-management.component.css'],
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    CardLocalComponent,
    EditLocalComponent,
  ],
  standalone: true,
})
export class LocalManagementComponent implements OnInit, OnDestroy {
  viewMode: 'cards' | 'table' = 'cards';
  showCreateForm = false;
  searchTerm = '';
  isLoading = false;
  showEditForm = false;
  selectedLocal: Local | null = null;

  siteId?: number;
  architecture2DFile?: File;
  localeImageFile?: File;
  architecture2DPreview?: string;
  localeImagePreview?: string;
  newTag: string = '';
  tags: string[] = [];
  site?: Site;

  locals: Local[] = [];
  filteredLocals: Local[] = [];

  currentPage: number = 1;
  pageSize: number = 3; // Display 3 items per page
  totalPages: number = 1;
  mqttSubscriptions: Subscription[] = [];
  devices: { [localId: number]: any[] } = {};

  // Add getter for paginated locals
  get paginatedLocals(): Local[] {
    const startIndex = (this.currentPage - 1) * this.pageSize;
    const endIndex = startIndex + this.pageSize;
    return this.filteredLocals.slice(startIndex, endIndex);
  }

  // Add pagination methods
  getPageNumbers(): number[] {
    this.totalPages = Math.ceil(this.filteredLocals.length / this.pageSize);
    if (this.totalPages <= 7) {
      return Array.from({ length: this.totalPages }, (_, i) => i + 1);
    }

    if (this.currentPage <= 4) {
      return [1, 2, 3, 4, 5, -1, this.totalPages];
    }

    if (this.currentPage >= this.totalPages - 3) {
      return [
        1,
        -1,
        this.totalPages - 4,
        this.totalPages - 3,
        this.totalPages - 2,
        this.totalPages - 1,
        this.totalPages,
      ];
    }

    return [
      1,
      -1,
      this.currentPage - 1,
      this.currentPage,
      this.currentPage + 1,
      -1,
      this.totalPages,
    ];
  }

  onPageChange(page: number): void {
    this.currentPage = page;
  }

  previousPage(): void {
    if (this.currentPage > 1) {
      this.currentPage--;
    }
  }

  nextPage(): void {
    if (
      this.currentPage < Math.ceil(this.filteredLocals.length / this.pageSize)
    ) {
      this.currentPage++;
    }
  }
  // Updated form to match the Local interface
  createLocalForm = new FormGroup({
    siteId: new FormControl(0, [Validators.required, Validators.min(1)]),
    type: new FormControl('', [Validators.required]),
    etage: new FormControl(0, [Validators.required]),
    surface: new FormControl(0, [Validators.required, Validators.min(0)]),
    hauteurSousPlafond: new FormControl(0, [
      Validators.required,
      Validators.min(0),
    ]),
    capacitePersonnes: new FormControl(0, [Validators.min(0)]),
    description: new FormControl(''),
    temperatureCible: new FormControl(20, [Validators.required]),
    consommationElectriqueMensuelle: new FormControl(0, [Validators.min(0)]),
    dateDerniereMaintenance: new FormControl('', [Validators.required]),
  });

  localTypes = [
    { value: 'Salle de réunion', label: 'Salle de réunion' },
    { value: 'Bureau', label: 'Bureau' },
    { value: 'Laboratoire', label: 'Laboratoire' },
    { value: 'Amphithéâtre', label: 'Amphithéâtre' },
    { value: 'Espace détente', label: 'Espace détente' },
    { value: 'Salle de formation', label: 'Salle de formation' },
  ];

  constructor(
    private readonly localService: LocalService,
    private readonly route: ActivatedRoute,
    private readonly mqttService: MqttService,
    private readonly siteService: SiteService,
    readonly router: Router
  ) {}

  ngOnInit(): void {
    this.route.params.subscribe((params) => {
      this.siteId = params['siteId'] ? +params['siteId'] : undefined;
      console.log('SiteId from route:', this.siteId);
      if (this.siteId) {
        this.loadLocals();
        this.loadSite();
        this.initializeCreateForm();
      } else {
        console.error('No siteId provided in route');
      }
    });
    this.filteredLocals = [...this.locals];
  }

  ngOnDestroy(): void {
    // Unsubscribe from all MQTT topics
    this.mqttSubscriptions.forEach((sub) => sub.unsubscribe());
  }

  private loadLocals(): void {
    this.isLoading = true;
    const request = this.siteId
      ? this.localService.getLocalsBySite(this.siteId)
      : this.localService.getLocals();

    request.subscribe({
      next: (data) => {
        this.locals = data;
        this.filteredLocals = data;
        this.isLoading = false;
        // Subscribe to MQTT topics for each local
        this.subscribeToLocalTopics();
      },
      error: (error) => {
        console.error('Error loading locals:', error);
        this.isLoading = false;
      },
    });
  }

  private loadSite(): void {
    this.siteService.getSite(this.siteId!).subscribe({
      next: (site) => {
        this.site = site;
      },
      error: (err) => {
        console.error('Error loading site:', err);
      },
    });
    console.log("test" + this.siteService.getSite(this.siteId!).subscribe({
      next: (site) => {
        this.site = site;
      },
      error: (err) => {
        console.error('Error loading site:', err);
      },
    }));
    
  }

  goBack(): void {
    this.router.navigate(['/organisation-details', this.siteId]);
  }

  private subscribeToLocalTopics(): void {
    // Clear existing subscriptions
    this.mqttSubscriptions.forEach((sub) => sub.unsubscribe());
    this.mqttSubscriptions = [];

    this.locals.forEach((local) => {
      const topic = `${local.baseTopic}/#`; // Subscribe to all devices under the base topic
      const subscription = this.mqttService
        .observe(topic)
        .subscribe((message: IMqttMessage) => {
          this.handleMqttMessage(local.id, message);
        });
      this.mqttSubscriptions.push(subscription);
    });
  }

  private handleMqttMessage(localId: number, message: IMqttMessage): void {
    try {
      const payload = JSON.parse(message.payload.toString());
      const topic = message.topic;
      // Initialize devices array for the local if not exists
      if (!this.devices[localId]) {
        this.devices[localId] = [];
      }
      // Update or add device data
      const deviceIndex = this.devices[localId].findIndex(
        (d) => d.topic === topic
      );
      if (deviceIndex >= 0) {
        this.devices[localId][deviceIndex] = { topic, payload };
      } else {
        this.devices[localId].push({ topic, payload });
      }
    } catch (error) {
      console.error('Error parsing MQTT message:', error);
    }
  }

  onArchitecture2DSelected(event: Event): void {
    const file = (event.target as HTMLInputElement).files?.[0];
    if (file) {
      this.architecture2DFile = file;
      this.createPreviewUrl(file, 'architecture2D');
    }
  }

  onLocaleImageSelected(event: Event): void {
    const file = (event.target as HTMLInputElement).files?.[0];
    if (file) {
      this.localeImageFile = file;
      this.createPreviewUrl(file, 'locale');
    }
  }

  private createPreviewUrl(
    file: File,
    type: 'architecture2D' | 'locale'
  ): void {
    const reader = new FileReader();
    reader.onload = () => {
      if (type === 'architecture2D') {
        this.architecture2DPreview = reader.result as string;
      } else {
        this.localeImagePreview = reader.result as string;
      }
    };
    reader.readAsDataURL(file);
  }

  addTag(): void {
    if (this.newTag.trim() && !this.tags.includes(this.newTag.trim())) {
      this.tags.push(this.newTag.trim());
      this.newTag = '';
    }
  }

  removeTag(tag: string): void {
    this.tags = this.tags.filter((t) => t !== tag);
  }

  toggleViewMode(): void {
    this.viewMode = this.viewMode === 'cards' ? 'table' : 'cards';
  }

  filterLocals(): void {
    if (!this.searchTerm) {
      this.filteredLocals = [...this.locals];
      return;
    }

    const term = this.searchTerm.toLowerCase();
    this.filteredLocals = this.locals.filter((local) =>
      local.type.toLowerCase().includes(term)
    );
    this.currentPage = 1;
    this.totalPages = Math.ceil(this.filteredLocals.length / this.pageSize);
  }

  private initializeCreateForm(): void {
    this.createLocalForm.patchValue({
      siteId: this.siteId ?? null,
    });
    this.createLocalForm.get('siteId')?.disable();
  }

  showAddLocalForm(): void {
    this.showCreateForm = true;
    this.initializeCreateForm();
    setTimeout(() => {
      const element = document.getElementById('createLocalForm');
      if (element) {
        element.scrollIntoView({ behavior: 'smooth', block: 'start' });
      }
    });
  }

  hideAddLocalForm(): void {
    this.showCreateForm = false;
    this.createLocalForm.reset();
  }

  submitCreateForm(): void {
    if (this.createLocalForm.valid) {
      const formData = new FormData();
      const formValues = this.createLocalForm.value as LocalFormValues;

      // Ensure SiteId is properly set
      if (this.siteId) {
        formData.append('SiteId', this.siteId.toString());
      } else {
        console.error('No SiteId available');
        return;
      }

      // Type-safe form value handling
      (Object.keys(formValues) as Array<keyof LocalFormValues>).forEach(
        (key) => {
          if (
            key !== 'siteId' &&
            formValues[key] !== null &&
            formValues[key] !== undefined
          ) {
            const value = formValues[key];
            if (value !== null) {
              formData.append(key, value.toString());
            }
          }
        }
      );

      // Add images with type checking
      if (this.architecture2DFile instanceof File) {
        formData.append('ImageArchitecture2D', this.architecture2DFile);
      }
      if (this.localeImageFile instanceof File) {
        formData.append('ImageLocale', this.localeImageFile);
      }

      // Add tags
      this.tags.forEach((tag) => {
        formData.append('Tags', tag);
      });

      // Debug logging with proper type handling
      formData.forEach((value, key) => {
        const stringValue = value instanceof File ? value.name : String(value);
        console.log(`${key}: ${stringValue}`);
      });

      this.localService.createLocal(formData).subscribe({
        next: (newLocal) => {
          this.locals.push(newLocal);
          this.filterLocals();
          this.showCreateForm = false;
          this.createLocalForm.reset();
          this.loadLocals();
        },
        error: (error) => {
          console.error('Error creating local:', error);
          this.showError(error.error ?? 'Error creating local');
        },
      });
    }
  }

  // Helper method with proper type checking

  // Helper method to show all validation errors
  private markFormGroupTouched(formGroup: FormGroup) {
    Object.values(formGroup.controls).forEach((control) => {
      control.markAsTouched();
      if (control instanceof FormGroup) {
        this.markFormGroupTouched(control);
      }
    });
  }

  // Alternative approach using a helper method for cleaner code
  private buildFormData(): FormData {
    const formData = new FormData();
    const formValues = this.createLocalForm.value;

    // Define the mapping between form controls and their types
    const fieldMappings: Array<{
      key: keyof typeof formValues;
      transform?: (value: any) => string;
    }> = [
      { key: 'siteId', transform: (value) => value?.toString() },
      { key: 'type' },
      { key: 'etage', transform: (value) => value?.toString() },
      { key: 'surface', transform: (value) => value?.toString() },
      { key: 'hauteurSousPlafond', transform: (value) => value?.toString() },
      { key: 'capacitePersonnes', transform: (value) => value?.toString() },
      { key: 'description' },
      { key: 'temperatureCible', transform: (value) => value?.toString() },
      {
        key: 'consommationElectriqueMensuelle',
        transform: (value) => value?.toString(),
      },
      { key: 'dateDerniereMaintenance' },
    ];

    fieldMappings.forEach(({ key, transform }) => {
      const value = formValues[key];
      if (value !== null && value !== undefined && value !== '') {
        const stringValue = transform ? transform(value) : String(value);
        formData.append(key, stringValue);
      }
    });

    return formData;
  }

  // Alternative submitCreateForm using the helper method
  submitCreateFormAlternative(): void {
    if (this.createLocalForm.valid) {
      const formData = this.buildFormData();

      this.localService.createLocal(formData).subscribe({
        next: (newLocal) => {
          this.locals.push(newLocal);
          this.filterLocals();
          this.showCreateForm = false;
          this.createLocalForm.reset();
        },
        error: (error) => {
          console.error('Error creating local:', error);
        },
      });
    }
  }

  viewLocalDetails(id: number): void {
    this.selectedLocal = this.locals.find((local) => local.id === id) || null;
    if (this.selectedLocal) {
      console.log(
        `Viewing devices for local ${this.selectedLocal.id} with baseTopic ${this.selectedLocal.baseTopic}`
      );
      // Devices are already loaded via MQTT subscriptions
    }
  }

  editLocal(id: number): void {
    this.selectedLocal = this.locals.find((local) => local.id === id) || null;
    this.showEditForm = true;
    setTimeout(() => {
      const element = document.getElementById('editLocalForm');
      if (element) {
        element.scrollIntoView({ behavior: 'smooth', block: 'start' });
      }
    });

    this.loadLocals();
  }

  onSaveChanges(event: LocalUpdateEvent): void {
    this.isLoading = true;

    this.localService
      .updateLocal(event.updatedLocal.id, event.formData)
      .subscribe({
        next: (updatedLocal) => {
          if (updatedLocal && updatedLocal.id) {
            // Reload the entire locals list
            this.loadLocals();

            // Reset the form and UI state
            this.showEditForm = false; // Explicitly close the form
            this.selectedLocal = null;

            // Optional: Scroll back to the cards view
            setTimeout(() => {
              const cardsView = document.querySelector('.cards-view-container');
              cardsView?.scrollIntoView({ behavior: 'smooth', block: 'start' });
            }, 100);

            // Show success message (optional)
            console.log('Local mis à jour avec succès');
          } else {
            console.error(
              'Received invalid response from server:',
              updatedLocal
            );
          }
          this.isLoading = false;
        },
        error: (error) => {
          console.error('Error updating local:', error);
          this.isLoading = false;
        },
        complete: () => {
          // Ensure the form is closed even if there's an error
          this.showEditForm = false;
          this.selectedLocal = null;
        },
      });
  }

  private showError(message: string): void {
    // Implement your error display logic here
    // For example, using a toast or alert service
    alert(message);
  }

  onCancelEdit(): void {
    this.showEditForm = false;
    this.selectedLocal = null;
  }

  deleteLocal(id: number): void {
    if (confirm('Êtes-vous sûr de vouloir supprimer ce local?')) {
      this.localService.deleteLocal(id).subscribe({
        next: () => {
          this.locals = this.locals.filter((local) => local.id !== id);
          this.filterLocals();
        },
        error: (error) => {
          console.error('Error deleting local:', error);
        },
      });
    }
  }

  getLocalTypeLabel(typeValue: string): string {
    return (
      this.localTypes.find((t) => t.value === typeValue)?.label ?? typeValue
    );
  }
  getTotalConsumption(): number {
    return this.locals.reduce(
      (sum, local) => sum + local.consommationElectriqueMensuelle,
      0
    );
  }

  getTotalCapacity(): number {
    return this.locals.reduce((sum, local) => sum + local.capacitePersonnes, 0);
  }

  getTotalSurface(): number {
    return this.locals.reduce((sum, local) => sum + local.surface, 0);
  }
}*/
