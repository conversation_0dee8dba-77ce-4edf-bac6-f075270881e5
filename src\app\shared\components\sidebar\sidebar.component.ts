// src/app/shared/components/sidebar/sidebar.component.ts (updated)
import { Component, EventEmitter, OnInit, Output } from '@angular/core';
import { RouterLink, RouterLinkActive } from '@angular/router';
import { MatIconModule } from '@angular/material/icon';
import { AuthService } from '@app/core/services/auth.service';
import { trigger, state, style, animate, transition } from '@angular/animations';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-sidebar',
  standalone: true,
  imports: [RouterLink, RouterLinkActive, MatIconModule, CommonModule],
  templateUrl: './sidebar.component.html',
  styleUrls: ['./sidebar.component.css'],
  animations: [
    trigger('toggleIconAnimation', [
      state('expanded', style({ transform: 'rotate(0deg)' })),
      state('collapsed', style({ transform: 'rotate(180deg)' })),
      transition('expanded <=> collapsed', [animate('0.3s cubic-bezier(0.4, 0, 0.2, 1)')
    ])
    ]),
    trigger('toggleIconAnimation', [
      state('expanded', style({ transform: 'rotate(0deg)' })),
      state('collapsed', style({ transform: 'rotate(180deg)' })),
      transition('expanded <=> collapsed', animate('300ms ease-in-out'))
    ])
  ]
})
export class SidebarComponent implements OnInit {
  @Output() sidebarToggled = new EventEmitter<boolean>(); // Renamed from toggleSidebar
  isCollapsed = false;
  isAdmin = false;
  isSuperAdmin = false;
  showParametrage = false;
  showOrganisation = false;
  showSites = false;
  showLocaux = false;

  constructor(readonly authService: AuthService) {}

  ngOnInit(): void {
    const roles = this.authService.getRoles();
    this.isAdmin = roles.includes('Admin');
    this.isSuperAdmin = roles.includes('SuperAdmin');
  }

  toggleSidebar(): void {
    this.isCollapsed = !this.isCollapsed;
    this.sidebarToggled.emit(this.isCollapsed); // Updated to use sidebarToggled
  }

  toggleParametrage(): void {
    this.showParametrage = !this.showParametrage;
    if (!this.showParametrage) {
      // Close all submenus when paramétrage is closed
      this.showOrganisation = false;
      this.showSites = false;
      this.showLocaux = false;
    }
  }

  toggleOrganisation(): void {
    this.showOrganisation = !this.showOrganisation;
    // Close other submenus
    if (this.showOrganisation) {
      this.showSites = false;
      this.showLocaux = false;
    }
  }

  toggleSites(): void {
    this.showSites = !this.showSites;
    // Close other submenus
    if (this.showSites) {
      this.showOrganisation = false;
      this.showLocaux = false;
    }
  }

  toggleLocaux(): void {
    this.showLocaux = !this.showLocaux;
    // Close other submenus
    if (this.showLocaux) {
      this.showOrganisation = false;
      this.showSites = false;
    }
  }
}