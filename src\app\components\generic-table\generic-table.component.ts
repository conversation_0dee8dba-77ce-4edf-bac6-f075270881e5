import { Component, Input, Output, EventEmitter, ViewChild, AfterViewInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatIconModule } from '@angular/material/icon';
import { MatPaginator, MatPaginatorModule, PageEvent } from '@angular/material/paginator';
import { MatMenuModule } from '@angular/material/menu';
import { MatButtonModule } from '@angular/material/button';

@Component({
  selector: 'app-generic-table',
  templateUrl: './generic-table.component.html',
  styleUrls: ['./generic-table.component.css'],
  standalone: true,
  imports: [CommonModule, MatIconModule, MatPaginatorModule, MatMenuModule, MatButtonModule]
})
export class GenericTableComponent implements AfterViewInit {
  @Input() headers: string[] = [];                
  @Input() keys: string[] = [];                   
  @Input() data: any[] = [];                      
  @Input() actions: string[] = [];                

  // Pagination Inputs
  @Input() pageSize: number = 10;
  @Input() currentPage: number = 0; // MatPaginator uses 0-based index
  @Input() totalCount: number = 0; // Total number of items

  // Pagination Output
  @Output() pageChange = new EventEmitter<PageEvent>();

  @Output() actionTriggered = new EventEmitter<{ action: string, row: any }>();

  @ViewChild(MatPaginator) paginator!: MatPaginator;

  ngAfterViewInit(): void {
    if (this.data && this.totalCount === 0) {
      this.totalCount = this.data.length;
    }
  }

  triggerAction(action: string, row: any): void {
    this.actionTriggered.emit({ action, row });
  }

  getActionIcon(action: string): string {
    switch (action) {
      case 'view': return 'visibility';
      case 'edit': return 'edit';
      case 'delete': return 'delete';
      default: return '';
    }
  }

  getActionClass(action: string): string {
    switch (action) {
      case 'view': return 'action-view';
      case 'edit': return 'action-edit';
      case 'delete': return 'action-delete';
      default: return '';
    }
  }

  // New helper function to get French labels for actions
  getActionLabel(action: string): string {
    switch (action) {
      case 'view': return 'Détails';
      case 'edit': return 'Modifier';
      case 'delete': return 'Supprimer';
      default: return action; // Fallback to original action name if not found
    }
  }

  onPageChange(event: PageEvent): void {
    this.currentPage = event.pageIndex;
    this.pageSize = event.pageSize;
    this.pageChange.emit(event);
  }


get pagedData(): any[] {
  if (!this.data || this.data.length === 0) {
    console.log('No data available for pagedData');
    return [];
  }
  const startIndex = this.currentPage * this.pageSize;
  const endIndex = startIndex + this.pageSize;
  const paged = this.data.slice(startIndex, Math.min(endIndex, this.data.length));
  return paged;
}
}