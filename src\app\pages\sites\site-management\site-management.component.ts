import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router } from '@angular/router';
import { FormsModule, ReactiveFormsModule, FormGroup, FormControl, Validators } from '@angular/forms';
import { SiteApiService } from '../../../core/services/administrative/site.service';
import { Site } from '../../../core/models/site';
import { trigger, state, style, transition, animate } from '@angular/animations';
import { GenericTableComponent } from '../../../components/generic-table/generic-table.component'; // Import GenericTableComponent
import { MatPaginatorModule, PageEvent } from '@angular/material/paginator'; // Import MatPaginatorModule and PageEvent
import { MatIconModule } from '@angular/material/icon'; // Import MatIconModule for material icons
import { Client } from '@app/core/models/client';
import { ClientApiService } from '@app/core/services/administrative/client.service';

interface SiteFormValues {
  nom: string | null;
  type: string | null;
  etage: string | null;
  rue: string | null;
  region: string | null;
  ville: string | null;
  pays: string | null;
  fuseauHoraire: string | null;
  latitude: number | null;
  longitude: number | null;
  idOrganisation: number | null;
}
enum OrganisationType {
  Ecole = 'Ecole',
  Hopital = 'Hopital',
  Magasin = 'Magasin',
  Usine = 'Usine',
  Restaurant = 'Restaurant',
  Residence = 'Residence',
  Hotel = 'Hotel',
  Maison = 'Maison',
  CentreCommercial = 'CentreCommercial',
  Entrepot = 'Entrepot',
  Bureau = 'Bureau'
}

@Component({
  selector: 'app-site-management',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    GenericTableComponent, // Add GenericTableComponent to imports
    MatPaginatorModule,    // Add MatPaginatorModule to imports
    MatIconModule          // Add MatIconModule to imports
  ],
  templateUrl: './site-management.component.html',
  styleUrls: ['./site-management.component.css'],
  animations: [
    trigger('tableRowAnimation', [
      state('void', style({ opacity: 0, transform: 'translateY(20px)' })),
      transition('void => *', animate('300ms ease-in'))
    ]),
    trigger('fadeIn', [
      state('void', style({ opacity: 0 })),
      transition('void => *', animate('400ms 300ms ease-in'))
    ])
  ]
})
export class SiteManagementComponent implements OnInit {
  sites: Site[] = [];;
  filteredSites: Site[] = []; // This will hold the currently displayed page data

  clients: Client[] = [];
filteredClients: Client[] = [];
  searchTerm: string = '';
  isLoading: boolean = true;
  showCreateForm: boolean = false;
  uploadedImages: File[] = [];
  viewMode: string = 'table';
  currentPage: number = 0; // MatPaginator uses 0-based index
  pageSize: number = 5;
  totalCount: number = 0; // Total count of filtered items
  siteImages: { [key: number]: string } = {};
  showEditForm: boolean = false;
  selectedSite: Site | null = null;
  editUploadedImages: File[] = [];
  removedImageIds: number[] = [];

  organizationTypes = Object.values(OrganisationType);
  selectedOrgType: OrganisationType = OrganisationType.Bureau;

  // Define headers and keys for the generic table
// Update these to match your Site interface exactly
headers: string[] = ['Nom', 'Ville', 'Pays', 'Rue', 'Téléphone', 'Statut'];
keys: string[] = ['Nom', 'Ville', 'Pays', 'Rue', 'TelephoneSurSite', 'IsEnabled'];
  siteTypesByOrg: { [key in OrganisationType]: string[] } = {
    [OrganisationType.Ecole]: ["Classe", "Laboratoire", "Bibliothèque", "Cantine"],
    [OrganisationType.Hopital]: ["Urgences", "Radiologie", "Bloc opératoire", "Laboratoire"],
    [OrganisationType.Magasin]: ["Caisse", "Réserve", "Rayon", "Parking"],
    [OrganisationType.Usine]: ["Production", "Magasin", "Qualité", "Maintenance"],
    [OrganisationType.Restaurant]: ["Cuisine", "Salle", "Réception"],
    [OrganisationType.Residence]: ["Appartement", "Salle commune", "Parking"],
    [OrganisationType.Hotel]: ["Chambre", "Réception", "Salle de conférence"],
    [OrganisationType.Maison]: ["Salon", "Cuisine", "Salle de bain"],
    [OrganisationType.CentreCommercial]: ["Boutique", "Aire de restauration", "Parking"],
    [OrganisationType.Entrepot]: ["Stockage", "Chargement", "Bureau"],
    [OrganisationType.Bureau]: ["Open Space", "Salle de réunion", "Direction"]
  };

  createSiteForm = new FormGroup({
    Nom: new FormControl('', [Validators.required]),
    Pays: new FormControl('', [Validators.required]),
    Ville: new FormControl('', [Validators.required]),
    Rue: new FormControl(''),
    TelephoneSurSite: new FormControl(''),
    Description: new FormControl(''),
    Images: new FormControl(null),
    ContactDeSite: new FormControl(''),
    IsEnabled: new FormControl(true),
    IdClient: new FormControl('', [Validators.required])
  });
  editSiteForm = new FormGroup({
    Id: new FormControl(''),
  Nom: new FormControl('', [Validators.required]),
  Pays: new FormControl('', [Validators.required]),
  Ville: new FormControl('', [Validators.required]),
  Rue: new FormControl(''),
  TelephoneSurSite: new FormControl(''),
  Description: new FormControl(''),
  Images: new FormControl(null),
  ContactDeSite: new FormControl(''),
  IsEnabled: new FormControl(true),
  IdClient: new FormControl('', [Validators.required])
});

  constructor(
    private readonly clientApiService: ClientApiService,
    private readonly siteService: SiteApiService,
    private readonly router: Router,
    private readonly route: ActivatedRoute
  ) {}

  ngOnInit(): void {
    this.loadSites();
    this.loadClients();
    this.route.queryParams.subscribe(params => {
      if (params['action'] === 'create') {
        this.showAddSiteForm();
      }
    });
  }
  loadClients(): void {
  this.isLoading = true;
  this.clientApiService.getAll().subscribe({
    next: (clients) => {
      // Transform the API response to match your interface
      this.clients = clients
      console.log(this.clients);
      this.isLoading = false;
    },
    error: (error) => {
      console.error('Error loading clients:', error);
      this.isLoading = false;
    },
  });
}

  loadSites(): void {
    this.isLoading = true;
    // Convert from 0-based to 1-based index for the API
    const apiPage = this.currentPage + 1;
    
    this.siteService.getPaginated(apiPage, this.pageSize).subscribe({
      next: (response) => {
        this.filteredSites = response.sites;
        this.totalCount = response.totalCount;
        this.isLoading = false;
        
        console.log('Loaded sites:', this.filteredSites);
        console.log('Total count:', this.totalCount);
      },
      error: (error) => {
        console.error('Error loading sites:', error);
        this.isLoading = false;
      }
    });
  }


  
  // Modified: Now accepts PageEvent from MatPaginator
  onPageChange(event: PageEvent): void {
    console.log('Page changed:', event);
    this.currentPage = event.pageIndex;
    this.pageSize = event.pageSize;
    this.loadSites(); // Reload data with new pagination parameters
  }

  // Updated filterSites method to work with server-side filtering
  filterSites(): void {
    this.currentPage = 0; // Reset to first page when filtering
    
    if (this.searchTerm.trim()) {
      this.isLoading = true;
      // Convert from 0-based to 1-based index for the API
      const apiPage = this.currentPage + 1;
      
      this.siteService.searchPaginated(this.searchTerm, apiPage, this.pageSize).subscribe({
        next: (response) => {
          this.filteredSites = response.sites;
          this.totalCount = response.totalCount;
          this.isLoading = false;
        },
        error: (error) => {
          console.error('Error searching sites:', error);
          this.isLoading = false;
        }
      });
    } else {
      this.loadSites(); // If search term is empty, load regular paginated data
    }
  }

  /*private updatePaginatedResults(): void {
    const startIndex = this.currentPage * this.pageSize;
    const endIndex = startIndex + this.pageSize;
    this.filteredSites = this.allFilteredSites.slice(startIndex, endIndex);
  }*/

  showAddSiteForm(): void {
    this.showCreateForm = true;
  }

  hideAddSiteForm(): void {
    this.showCreateForm = false;
    this.createSiteForm.reset();
    this.uploadedImages = [];
  }

  onImagesSelected(event: any): void {
    if (event.target.files && event.target.files.length > 0) {
      this.uploadedImages = Array.from(event.target.files);
    }
  }

   generateUUID(): string {
      return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
        const r = Math.random() * 16 | 0;
        const v = c === 'x' ? r : (r & 0x3 | 0x8);
        return v.toString(16);
      });
    }

  submitCreateForm(): void {
    if (this.createSiteForm.valid) {
      const formValues = this.createSiteForm.value;
      const site: Site = {
        Id: this.generateUUID(),
        Nom: formValues.Nom ?? '',
        Pays: formValues.Pays ?? '',
        Ville: formValues.Ville ?? '',
        Rue: formValues.Rue ?? '',
        TelephoneSurSite: formValues.TelephoneSurSite ?? '',
        Description: formValues.Description ?? '',
        Images: '', // Will be set below
        ContactDeSite: formValues.ContactDeSite ?? '',
        IsEnabled: formValues.IsEnabled ?? true,
        IdClient: formValues.IdClient ?? '',
        CreatedAt: new Date().toISOString()
      };

      if (this.uploadedImages && this.uploadedImages.length > 0) {
        const file = this.uploadedImages[0];
        const reader = new FileReader();
        reader.onload = () => {
          site.Images = (reader.result as string).split(',')[1] ?? 'null';
          this.siteService.create(site).subscribe({
            next: () => {
              this.loadSites();
              this.hideAddSiteForm();
            },
            error: (error) => {
              console.error('Error creating site:', error);
            }
          });
        };
        reader.readAsDataURL(file);
      } else {
        console.log(site.Images);
        // No image selected
        this.siteService.create(site).subscribe({
          next: () => {
            this.loadSites();
            this.hideAddSiteForm();
          },
          error: (error) => {
            console.error('Error creating site:', error);
          }
        });
      }
    }
  }

  editSite(id: string): void {
  console.log('Edit triggered for ID:', id); // Debug
  const site = this.sites.find(s => s.Id === id);
  console.log('Found site:', site); // Debug
  
  if (site) {
    this.selectedSite = site;
    console.log('Patching form with:', { // Debug
      Nom: site.Nom,
      Pays: site.Pays,
      Ville: site.Ville,
      // ... other fields
    });
    
    this.editSiteForm.patchValue({
      Id: site.Id,
      Nom: site.Nom,
      Pays: site.Pays,
      Ville: site.Ville,
      Rue: site.Rue,
      TelephoneSurSite: site.TelephoneSurSite,
      Description: site.Description,
      ContactDeSite: site.ContactDeSite,
      IsEnabled: site.IsEnabled,
      IdClient: site.IdClient
    });
    
    this.showEditForm = true;
    console.log('Edit form should be visible. showEditForm:', this.showEditForm); // Debug
  } else {
    console.warn('Site not found with ID:', id); // Debug
  }
}

  deleteSite(id: string): void {
    if (confirm('Êtes-vous sûr de vouloir supprimer ce site ?')) {
      this.siteService.delete(id).subscribe({
        next: () => {
          this.loadSites();
        },
        error: (error) => {
          console.error('Error deleting site:', error);
        }
      });
    }
  }

  viewDetails(id: string): void {
    this.router.navigate(['/site-locals/', id]);
  }

  hideEditForm(): void {
    this.showEditForm = false;
    this.selectedSite = null;
    this.editSiteForm.reset();
    this.editUploadedImages = [];
  }

  onEditImagesSelected(event: any): void {
    if (event.target.files && event.target.files.length > 0) {
      this.editUploadedImages = Array.from(event.target.files);
    }
  }

submitEditForm(): void {
  if (this.editSiteForm.valid && this.selectedSite) {
    const formValues = this.editSiteForm.value;
    
    // Create updated site object
    const updatedSite: Site = {
      Id: formValues.Id ?? this.selectedSite.Id, // Ensure we have the ID
      Nom: formValues.Nom ?? this.selectedSite.Nom,
      Pays: formValues.Pays ?? this.selectedSite.Pays,
      Ville: formValues.Ville ?? this.selectedSite.Ville,
      Rue: formValues.Rue ?? this.selectedSite.Rue,
      TelephoneSurSite: formValues.TelephoneSurSite ?? this.selectedSite.TelephoneSurSite,
      Description: formValues.Description ?? this.selectedSite.Description,
      Images: this.selectedSite.Images, // Keep existing or handle new images
      ContactDeSite: formValues.ContactDeSite ?? this.selectedSite.ContactDeSite,
      IsEnabled: formValues.IsEnabled ?? this.selectedSite.IsEnabled,
      IdClient: formValues.IdClient ?? this.selectedSite.IdClient,
      CreatedAt: this.selectedSite.CreatedAt // Preserve original creation date
    };

    // Handle image upload if new images were selected
    if (this.editUploadedImages && this.editUploadedImages.length > 0) {
      const file = this.editUploadedImages[0];
      const reader = new FileReader();
      reader.onload = () => {
        updatedSite.Images = (reader.result as string).split(',')[1];;
        this.updateSite(updatedSite);
      };
      reader.readAsDataURL(file);
    } else {
      this.updateSite(updatedSite);
    }
  }
}

private updateSite(updatedSite: Site): void {
  this.siteService.put("", updatedSite).subscribe({
    next: (response) => {
      console.log('Site updated successfully:', response);
      this.loadSites(); // Refresh the list
      this.hideEditForm(); // Close the form
    },
    error: (error) => {
      console.error('Error updating site:', error);
      // Handle error (show message to user, etc.)
    }
  });
}

  // Handle actions triggered by the generic table
handleAction(event: { action: string; row: any }): void {
  const { action, row } = event;
  console.log('Action triggered:', action, row); // Debug log
  
  if (action === 'edit') {
    this.editSite(row.Id);  // Note: Using 'Id' instead of 'idClient'
  } else if (action === 'delete') {
    this.deleteSite(row.Id); // Note: Using 'Id' instead of 'idClient'
  } else if (action === 'view') {
    this.viewDetails(row.Id); // Note: Using 'Id' instead of 'idClient'
  }
}
}
