
<!-- local-details.component.html -->
<div class="local-container" *ngIf="local">
    <h2>{{ local.type }} - Devices</h2>
    <div class="device-grid">
      <div class="device-card" *ngFor="let device of devices">
        <div class="device-header">
          <h3>{{ device.name }}</h3>
          <span class="device-status" [class.online]="isRecent(device.lastSeen)">
            {{ isRecent(device.lastSeen) ? 'Online' : 'Offline' }}
          </span>
        </div>
        
        <div class="device-properties">
          <div *ngIf="device.type === 'light'">
            <p>State: {{ device.properties.state || 'unknown' }}</p>
            <p *ngIf="device.properties.brightness">Brightness: {{ device.properties.brightness }}</p>
          </div>
          
          <div *ngIf="device.type === 'sensor'">
            <p *ngIf="device.properties.temperature">Temperature: {{ device.properties.temperature }}°C</p>
            <p *ngIf="device.properties.humidity">Humidity: {{ device.properties.humidity }}%</p>
          </div>
          
          <div *ngIf="device.type === 'switch'">
            <p>State: {{ device.properties.state || 'unknown' }}</p>
          </div>
        </div>
        
        <div class="device-footer">
          <small>Last seen: {{ device.lastSeen | date:'short' }}</small>
        </div>
      </div>
    </div>
  </div>