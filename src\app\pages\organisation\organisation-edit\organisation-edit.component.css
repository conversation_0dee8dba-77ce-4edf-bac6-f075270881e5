.organisation-edit-container {
    max-width: 1000px;
    margin: 30px auto;
    padding: 25px;
    background: linear-gradient(145deg, #ffffff, #f9f9f9);
    border-radius: 12px;
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.08);
  }
  
  /* Breadcrumb navigation */
  .breadcrumb-nav {
    display: flex;
    align-items: center;
    margin-bottom: 25px;
  }
  
  .back-button {
    background: #f5f5f5;
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    cursor: pointer;
    transition: all 0.3s ease;
  }
  
  .back-button:hover {
    background: #e0e0e0;
    transform: translateX(-3px);
  }
  
  .back-button .material-icons {
    color: #555;
    font-size: 24px;
  }
  
  .breadcrumb-text {
    font-size: 16px;
    color: #666;
  }
  
  /* Loading spinner */
  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 250px;
    animation: fadeIn 0.5s ease-out;
  }
  
  .spinner {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    border: 3px solid rgba(76, 175, 80, 0.1);
    border-top-color: #4CAF50;
    animation: spin 1s linear infinite;
    margin-bottom: 20px;
  }
  
  @keyframes spin {
    to {
      transform: rotate(360deg);
    }
  }
  
  .loading-container p {
    color: #718096;
    font-size: 16px;
    font-style: italic;
  }
  
  /* Edit form */
  .edit-form-container {
    animation: fadeIn 0.5s ease-out;
  }
  
  .form-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    padding: 25px;
  }
  
  .form-title {
    margin: 0 0 20px;
    font-size: 24px;
    font-weight: 600;
    color: #2E7D32;
  }
  
  .current-logo {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 25px;
  }
  
  .logo-preview {
    width: 120px;
    height: 120px;
    object-fit: cover;
    border-radius: 6px;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  }
  
  .logo-caption {
    margin: 10px 0 0;
    font-size: 14px;
    color: #718096;
    font-style: italic;
  }
  
  .form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
    gap: 20px;
    margin-bottom: 25px;
  }
  
  .form-group {
    display: flex;
    flex-direction: column;
    margin-bottom: 15px;
  }
  
  .full-width {
    grid-column: 1 / -1;
  }
  
  .form-group label {
    font-size: 14px;
    color: #4a5568;
    margin-bottom: 6px;
    font-weight: 500;
  }
  
  .form-control {
    height: 40px;
    padding: 8px 12px;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    transition: all 0.3s ease;
  }
  
  .form-control:focus {
    border-color: #4CAF50;
    box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.2);
    outline: none;
  }
  
  .form-error {
    font-size: 12px;
    color: #e53e3e;
    margin-top: 4px;
  }
  
  .file-input-container {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-top: 6px;
  }
  
  .file-button {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 8px 12px;
    background-color: #edf2f7;
    color: #4a5568;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    transition: all 0.3s ease;
    cursor: pointer;
    font-size: 14px;
  }
  
  .file-button:hover {
    background-color: #e2e8f0;
  }
  
  .file-info {
    font-size: 14px;
    color: #718096;
  }
  
  .form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 15px;
  }
  
  .btn-cancel {
    padding: 10px 20px;
    background: transparent;
    border: 1px solid #cbd5e0;
    color: #718096;
    border-radius: 6px;
    transition: all 0.3s ease;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
  }
  
  .btn-cancel:hover {
    background-color: #f7fafc;
  }
  
  .btn-submit {
    padding: 10px 20px;
    background: linear-gradient(45deg, #4CAF50, #81C784);
    color: white;
    border: none;
    border-radius: 6px;
    transition: all 0.3s ease;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    box-shadow: 0 2px 6px rgba(76, 175, 80, 0.3);
  }
  
  .btn-submit:hover {
    background: linear-gradient(45deg, #81C784, #4CAF50);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(76, 175, 80, 0.4);
  }
  
  .btn-submit:disabled {
    background: #cbd5e0;
    transform: none;
    box-shadow: none;
    cursor: not-allowed;
  }
  
  /* Error container */
  .error-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 50px 0;
    text-align: center;
    color: #718096;
    animation: fadeIn 0.5s ease-out;
  }
  
  .error-icon {
    font-size: 48px;
    color: #e53e3e;
    margin-bottom: 15px;
  }
  
  .error-container p {
    margin: 0 0 20px;
    font-size: 16px;
  }
  
  .btn-primary {
    padding: 10px 20px;
    background: linear-gradient(45deg, #4CAF50, #81C784);
    color: white;
    border: none;
    border-radius: 6px;
    box-shadow: 0 2px 6px rgba(76, 175, 80, 0.3);
    transition: all 0.3s ease;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
  }
  
  .btn-primary:hover {
    background: linear-gradient(45deg, #81C784, #4CAF50);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(76, 175, 80, 0.4);
  }
  
  /* Animations */
  @keyframes fadeIn {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }
  
  /* Responsive design */
  @media (max-width: 768px) {
    .form-grid {
      grid-template-columns: 1fr;
    }
  }