<!-- src/app/app.component.html -->
<div class="app-container" [ngClass]="{'dark-mode': isDarkMode}" *ngIf="isLoggedIn">
  <app-sidebar (sidebarToggled)="onToggleSidebar($event)"></app-sidebar>
  <div class="main-content" [ngClass]="{'sidebar-collapsed': isSidebarCollapsed}">
    <app-header (themeToggled)="onToggleTheme($event)"></app-header>
    <router-outlet></router-outlet>
    <app-notifications-container></app-notifications-container>
  </div>
</div>