/* src/app/shared/components/sidebar/sidebar.component.css */
.sidebar {
  background-color: var(--container-bg);
  color: var(--card-bg);
  height: 100vh;
  position: fixed;
  top: 0;
  left: 0;
  width: 250px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border-right: 1px solid rgba(0, 0, 0, 0.12);
  overflow-x: hidden;
  overflow-y: hidden;
  z-index: 1200;
}

.sidebar.collapsed {
  width: 100px;
}

.sidebar-header {
  height: 66px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.12);
  display: flex;
  align-items: center;
  padding: 0 16px !important;
  position: relative;
}

.logo-container {
  display: flex;
  align-items: center;
  width: calc(100% - 16px); /* Adjust width to prevent overlap */
  gap: 12px;
  padding: 0 8px;
  position: relative;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Logo Styles */
.logo-wrapper {
  min-width: 40px;
  width: 65px;
  height: 65px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  background: white;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  flex-shrink: 0;
}

.logo {
  width: 100%;
  height: 100%;
  object-fit: contain;
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}




.title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--sidebar-title);
  white-space: nowrap;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  opacity: 1;
  flex-grow: 1;
}

.sidebar.collapsed .logo-wrapper {
  min-width: 36px;
  width: 36px;
  height: 36px;
}

.sidebar.collapsed .logo-container {
  justify-content: center;
  padding: 0 4px;
  width: calc(100% - 16px); 
}

.sidebar.collapsed .title {
  opacity: 0;
  transform: translateX(-20px);
  width: 0;
  flex-grow: 0;
  margin: 0;
}

/* Toggle Icon Container */
.toggle-button-container {
  position: absolute;
  right: -18px; 
  top: 16px;
  z-index: 1100;
  transition: all 0.3s ease;
}

/* Improved Toggle Icon */
/* Centrage et transition douce de l'icône même en sidebar réduite */
.toggle-icon {
  cursor: pointer;
  background-color: white;
  color: var(--primary-color, #67c987);
  width: 36px;
  height: 36px;
  border-radius: 50%;
  border: 1px solid #ddd;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: transform 0.3s ease, background-color 0.3s ease;
}

.toggle-button {
  position: absolute;
  right: -16px;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  background: white;
  border: 1px solid #e0e0e0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 1300;
  padding: 0; /* Remove padding */
}

.toggle-button mat-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  font-size: 24px;
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  margin: 0; /* Remove any margin */
  line-height: 1; /* Ensure proper vertical alignment */
}

/* Optionnel : Ajout d'une rotation douce */
.toggle-icon.collapsed mat-icon {
  transform: rotate(180deg);
}


.toggle-icon:hover {
  background-color: var(--hover-bg-color, #f5f5f5);
  transform: scale(1.1);
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.25);
}

.toggle-button:hover {
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  background-color: #ffffff;
}

.toggle-icon:active {
  transform: scale(0.95);
}

/* Sidebar Content */
.sidebar-content {
  padding: 10px 0;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  height: calc(100vh - 65px);
  gap: 2px;
}

/* Sidebar Items */
.sidebar-item {
  display: flex;
  align-items: center;
  padding: 10px 15px;
  color: var(--card-description);
  text-decoration: none;
  transition: background-color 0.3s ease, color 0.3s ease;
  border-radius: 16px;
  margin: 3px 8px;
  margin-right: 10px;
  position: relative;
  overflow: hidden;
}

.sidebar-item:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 0;
  height: 100%;
  background-color: var(--highlight-color, rgba(54, 107, 42, 0.107));
  transition: all 0.3s ease;
  z-index: -1;
}

.sidebar-item:hover:before {
  width: 100%;
}

.sidebar-item:hover {
  background-color: var(--hover-bg-color);
  color: var(--primary);
}

.sidebar-item.active {
  background-color: var(--active-item-bg);
  color: var(--primary);
  font-weight: 600;
}

.sidebar-item:hover mat-icon,
.sidebar-item.active mat-icon {
  color: var(--primary);
}

/* Add transform to the toggle icon when collapsed */
.sidebar.collapsed .toggle-icon mat-icon {
  transform: rotate(180deg);
}

.sidebar-item mat-icon {
  margin-right: 12px;
  font-size: 20px;
  width: 24px;
  height: 24px;
  transition: margin 0.3s ease;
}

.sidebar.collapsed .sidebar-item {
  justify-content: center;
  padding: 12px 0;
  border-radius: 16px; /* Keep the same border-radius as expanded state */
  margin: 3px 8px; /* Keep the same margins as expanded state */
   position: relative;
}

.sidebar.collapsed .toggle-button {
  right: -16px; /* Keep the button visible */
  background: white;
  transform: translateX(0);
}

.sidebar.collapsed .toggle-button mat-icon {
  transform: rotate(180deg);
}



.sidebar.collapsed .sidebar-item span {
  display: none;
}

.sidebar.collapsed .sidebar-item mat-icon {
  margin-right: 0;
}


.sidebar.collapsed .sidebar-header {
  justify-content: center;
  padding: 15px 0;
  height: 66px;
}

.sidebar.collapsed .sidebar-item:hover:after {
  content: attr(data-title);
  position: absolute;
  left: 70px; /* Adjusted to prevent overlapping */
  background-color: transparent;
  color: var(--card-title);
  padding: 6px 10px;
  border-radius: 4px;
  font-size: 12px;
  white-space: nowrap;
  z-index: 1002;
}

@keyframes logoSpin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}


/* Responsive adjustments */
@media (max-width: 768px) {
  .logo-wrapper {
    width: 36px;
    height: 36px;
  }
  
  .title {
    font-size: 1.1rem;
  }
  .sidebar {
    transform: translateX(0);
  }

  .sidebar.collapsed {
    transform: translateX(-100%);
  }

  .toggle-button-container {
    right: -16px;
  }

  .sidebar.collapsed .toggle-button-container {
    position: relative !important;
    right: auto !important;
    top: auto !important;
    transform: none !important;
    display: flex;
    align-items: center;
  }

  .sidebar.collapsed .toggle-button {
    right: -14px;
  }

  .toggle-button {
    width: 28px;
    height: 28px;
  }

  .toggle-button mat-icon {
    font-size: 20px;
    width: 20px;
    height: 20px;
  }

  .logo-container {
    width: calc(100% - 14px);
  }
}

/* Add these styles to your existing CSS */

.parent-menu {
  cursor: pointer;
}

.expand-icon {
  margin-left: auto !important;
  transition: transform 0.3s ease;
}

.submenu {
  margin-left: 8px;
}

.submenu-item {
  padding-left: 24px;
  cursor: pointer;
}

.nested-submenu {
  margin-left: 16px;
  overflow: hidden;
  transition: max-height 0.3s ease-out;
}

.nested-item {
  padding-left: 32px;
  font-size: 0.9em;
}

/* Animation for expand/collapse */
.submenu, .nested-submenu {
  animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Adjust for collapsed state */
.sidebar.collapsed .submenu,
.sidebar.collapsed .nested-submenu {
  display: none;
}

.sidebar.collapsed .parent-menu .expand-icon {
  display: none;
}
