import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormControl, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { Local, LocalTag } from '../../../core/models/local.model';
import { id } from '@swimlane/ngx-charts';


interface LocalUpdateEvent {
  updatedLocal: Local;
  formData: FormData;
}
@Component({
  selector: 'app-edit-local',
  templateUrl: './edit-local.component.html',
  styleUrls: ['./edit-local.component.css'],
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule,FormsModule]
})
export class EditLocalComponent implements OnInit {
  @Input() local!: Local;
  @Output() saveChanges = new EventEmitter<LocalUpdateEvent>();
  @Output() cancel = new EventEmitter<void>();

  editLocalForm!: FormGroup;
    architecture2DFile?: File;
  localeImageFile?: File;
  architecture2DPreview?: string;
  localeImagePreview?: string;
  newTag: string = '';
  tags: string[] = [];
  
  localTypes = [
    { value: 'class', label: 'Classe' },
    { value: 'laboratoire', label: 'Laboratoire' },
    { value: 'bureau', label: 'Bureau' },
    { value: 'salle_reunion', label: 'Salle de réunion' },
    { value: 'amphitheatre', label: 'Amphithéâtre' },
    { value: 'bibliotheque', label: 'Bibliothèque' }
  ];

  ngOnInit(): void {
    this.initializeForm();
        this.tags = this.local.tags.map(t => t.tag);

  }

  private initializeForm(): void {
    this.editLocalForm = new FormGroup({
      id: new FormControl(this.local.id, [Validators.required]),
      siteId: new FormControl(this.local.siteId, [Validators.required]),
      type: new FormControl(this.local.type, [Validators.required]),
      etage: new FormControl(this.local.etage, [Validators.required]),
      surface: new FormControl(this.local.surface, [Validators.required, Validators.min(0)]),
      hauteurSousPlafond: new FormControl(this.local.hauteurSousPlafond, [Validators.required, Validators.min(0)]),
      capacitePersonnes: new FormControl(this.local.capacitePersonnes, [Validators.required, Validators.min(0)]),
      description: new FormControl(this.local.description),
      temperatureCible: new FormControl(this.local.temperatureCible, [Validators.required]),
      consommationElectriqueMensuelle: new FormControl(this.local.consommationElectriqueMensuelle, [Validators.required, Validators.min(0)]),
      dateDerniereMaintenance: new FormControl(this.local.dateDerniereMaintenance)
    });
  }

    onArchitecture2DSelected(event: Event): void {
    const file = (event.target as HTMLInputElement).files?.[0];
    if (file) {
      this.architecture2DFile = file;
      this.createPreviewUrl(file, 'architecture2D');
    }
  }

  onLocaleImageSelected(event: Event): void {
    const file = (event.target as HTMLInputElement).files?.[0];
    if (file) {
      this.localeImageFile = file;
      this.createPreviewUrl(file, 'locale');
    }
  }

  private createPreviewUrl(file: File, type: 'architecture2D' | 'locale'): void {
    const reader = new FileReader();
    reader.onload = () => {
      if (type === 'architecture2D') {
        this.architecture2DPreview = reader.result as string;
      } else {
        this.localeImagePreview = reader.result as string;
      }
    };
    reader.readAsDataURL(file);
  }

  addTag(): void {
    if (this.newTag.trim() && !this.tags.includes(this.newTag.trim())) {
      this.tags.push(this.newTag.trim());
      this.newTag = '';
    }
  }

  removeTag(tag: string): void {
    this.tags = this.tags.filter(t => t !== tag);
  }

  onSubmit(): void {
    if (this.editLocalForm.valid) {
      const formData = new FormData();
      const formValues = this.editLocalForm.value;

      // Add basic form values
      Object.keys(formValues).forEach(key => {
        if (formValues[key] !== null && formValues[key] !== undefined) {
          formData.append(key, formValues[key]);
        }
      });

      // Add images with correct field names
      if (this.architecture2DFile) {
        formData.append('ImageArchitecture2D', this.architecture2DFile);  // <- Note the exact name
      }
      if (this.localeImageFile) {
        formData.append('ImageLocale', this.localeImageFile);  // <- Note the exact name
      }

      // Add tags
      this.tags.forEach(tag => {
        formData.append('tags', tag);
      });

      this.saveChanges.emit({
        updatedLocal: {
          ...this.local,
          ...formValues,
          tags: this.tags.map(tag => ({ tag, localId: this.local.id }))
        },
        formData
      });
    }
  }
    onCancel(): void {
    this.cancel.emit();
  }
}