export interface LocalTag {
  id: number;
  localId: number;
  tag: string;
}

export interface Local {
  id: number;
  nom: string;
  type: string;
  baseTopic: string;
  etage: number;
  surface: number;
  hauteurSousPlafond: number;
  capacitePersonnes: number;
  description: string;
  temperatureCible: number;
  consommationElectriqueMensuelle: number;
  dateDerniereMaintenance: string;
  siteId: number;
  imageArchitecture2D: string;
  imageLocale: string;
  tags: LocalTag[]; // Add this property
}