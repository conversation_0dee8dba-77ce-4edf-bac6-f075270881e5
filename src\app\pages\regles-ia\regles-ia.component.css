.regles-container {
  min-height: 100vh;
  background-color: #f9fafb;
  padding: 2rem;
}

/* Header Styles */
.header {
  margin-bottom: 2rem;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background: white;
  border-radius: 1rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

h1 {
  font-size: 2rem;
  font-weight: 700;
  color: #111827;
  margin-bottom: 0.5rem;
}

.header-content p {
  color: #6b7280;
  font-size: 1.1rem;
}

.btn-create {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background-color: #4CAF50;
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  font-weight: 500;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-create:hover {
  background-color: #43a047;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(76, 175, 80, 0.2);
}

/* Tabs Styles */
.tabs {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 2rem;
  padding: 0.5rem;
  background: white;
  border-radius: 0.75rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.tabs button {
  padding: 0.75rem 1.25rem;
  border-radius: 0.5rem;
  font-weight: 500;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #6b7280;
  background: transparent;
}

.tabs button:hover:not(.active) {
  background-color: #f3f4f6;
  color: #111827;
}

.tabs button.active {
  background-color: #4CAF50;
  color: white;
}

/* Rules Grid Styles */
.rules-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.rule-card {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 1rem;
  padding: 1.5rem;
  transition: all 0.3s ease;
}

.rule-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.08);
  border-color: #4CAF50;
}

/* Card Header Styles */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1.25rem;
}

.icons-group {
  display: flex;
  gap: 0.5rem;
}

.icon-wrapper {
  padding: 0.5rem;
  background-color: #f0fdf4;
  border-radius: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.icon-wrapper mat-icon {
  color: #4CAF50;
  font-size: 1.25rem;
}

.ai-badge {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.25rem 0.75rem;
  background-color: #f0fdf4;
  color: #4CAF50;
  border-radius: 1rem;
  font-size: 0.875rem;
  font-weight: 500;
}

.confidence {
  color: #4CAF50;
  font-weight: 600;
  font-size: 0.875rem;
}

/* Rule Details Styles */
.rule-details {
  margin: 1.25rem 0;
}

.label {
  text-transform: uppercase;
  color: #6b7280;
  font-size: 0.75rem;
  font-weight: 600;
  letter-spacing: 0.05em;
  display: block;
  margin-bottom: 0.5rem;
}

.trigger-section p {
  color: #374151;
  font-size: 0.925rem;
  line-height: 1.5;
}

.actions-section ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.actions-section li {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #374151;
  font-size: 0.925rem;
  margin-bottom: 0.5rem;
}

.actions-section mat-icon {
  color: #4CAF50;
  font-size: 1rem;
}

/* Card Footer Styles */
.card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 1.5rem;
  padding-top: 1.25rem;
  border-top: 1px solid #e5e7eb;
}

.status {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #4CAF50;
  font-weight: 500;
}

.status-dot {
  width: 0.5rem;
  height: 0.5rem;
  background-color: #4CAF50;
  border-radius: 50%;
}

.btn-add {
  padding: 0.5rem 1rem;
  background-color: #4CAF50;
  color: white;
  border: none;
  border-radius: 0.5rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-add:hover {
  background-color: #43a047;
  transform: translateY(-1px);
}

.btn-settings {
  padding: 0.5rem;
  border: none;
  border-radius: 0.5rem;
  background: transparent;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-settings:hover {
  background-color: #f3f4f6;
  color: #111827;
}

/* AI Insights Section */
.ai-insights {
  background: white;
  border-radius: 1rem;
  padding: 1.5rem;
  margin-top: 2rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.insights-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1.5rem;
}

.insights-header mat-icon {
  color: #4CAF50;
  font-size: 1.5rem;
}

.insights-header h2 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #111827;
}

.insights-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  gap: 1rem;
}

.insight-card {
  background-color: #f0fdf4;
  padding: 1.25rem;
  border-radius: 0.75rem;
  text-align: center;
}

.insight-card .value {
  font-size: 1.5rem;
  font-weight: 700;
  color: #4CAF50;
  margin-bottom: 0.5rem;
}

.insight-card .label {
  color: #374151;
  font-size: 0.875rem;
  text-transform: none;
  letter-spacing: normal;
}

/* Responsive Design */
@media (max-width: 768px) {
  .regles-container {
    padding: 1rem;
  }

  .header-content {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }

  .insights-grid {
    grid-template-columns: 1fr;
  }

  .rules-grid {
    grid-template-columns: 1fr;
  }
}