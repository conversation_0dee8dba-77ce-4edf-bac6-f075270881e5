<!-- src/app/shared/components/header/header.component.html -->
<header class="header">
  <div class="header-left">
    <button 
      class="sidebar-toggle" 
      (click)="toggleSidebar()" 
      matTooltip="Toggle Sidebar"
      matTooltipPosition="right"
      aria-label="Toggle Sidebar"
    >
      <mat-icon>menu</mat-icon>
    </button>
  </div>
  
    <div class="profile" (click)="toggleProfileMenu()" matTooltip="Profile">
      <mat-icon>account_circle</mat-icon>
      <span class="user-name" *ngIf="!isSidebarCollapsed">{{ userName }}</span>
    </div>
    <div class="profile-dropdown" *ngIf="showProfileMenu" [@dropdownAnimation]>
      <div class="profile-item">
        <mat-icon>person</mat-icon>
        <span>{{ userRole }}</span>
      </div>
      <div class="profile-item" (click)="logout()">
        <mat-icon>logout</mat-icon>
        <span>Logout</span>
      </div>
    </div>
</header>