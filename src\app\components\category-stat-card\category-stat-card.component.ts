import { Component, Input, OnInit, Output, EventEmitter  } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { OrganisationService, OrganisationType } from '../../core/services/organisation.service';
import { Router } from '@angular/router';

@Component({
  selector: 'app-category-stat-card',
  standalone: true,
  imports: [CommonModule,RouterModule],
  templateUrl: './category-stat-card.component.html',
  styleUrls: ['./category-stat-card.component.css']
})
export class CategoryStatCardComponent implements OnInit {
  @Input() type: string = '';
  @Input() count: number = 0;
  @Output() viewDetails = new EventEmitter<string>();
  icon: string = 'location_on';
  cardColor: string = '#4CAF50';

  constructor(readonly organisationService: OrganisationService,private router: Router) {}

  ngOnInit(): void {
    const orgType = this.getOrganisationTypeFromString(this.type);
    this.icon = this.organisationService.getIconForOrganisationType(orgType);
    this.cardColor = this.organisationService.getColorForOrganisationType(orgType);
  }

  onDetailsClick(event: Event, type: string): void {
    // event.stopPropagation(); // Prevent triggering the card click
    // this.viewDetails.emit(this.type);
    this.router.navigate(['/type-organisations', type]);
  }

  private getOrganisationTypeFromString(type: string): OrganisationType {
    // Safely convert the string to OrganisationType
    const validTypes: OrganisationType[] = [
      'Ecole', 'Hopital', 'Entrepot', 'Bureau', 'Usine', 
      'Magasin', 'Residence', 'CentreCommercial', 'Restaurant', 
      'Hotel', 'Maison'
    ];
    
    return validTypes.includes(type as OrganisationType) 
      ? type as OrganisationType 
      : 'Bureau'; // default value
  }
}