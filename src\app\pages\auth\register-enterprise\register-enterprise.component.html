<div class="register-container" [@fadeIn]>
    <mat-card class="register-card" [@cardAnimation]>
      <mat-card-header class="header">
        <mat-card-title class="title">
          <mat-icon class="title-icon">business</mat-icon>
          Inscription Entreprise
        </mat-card-title>
        <mat-card-subtitle class="subtitle">Créer un nouveau compte entreprise</mat-card-subtitle>
      </mat-card-header>
      <mat-card-content class="content">
        <form [formGroup]="registerForm" (ngSubmit)="onSubmit()">
          <mat-form-field appearance="outline" class="full-width" floatLabel="always">
            <mat-label>Nom d'utilisateur</mat-label>
            <input matInput formControlName="userName" required>
            <mat-icon matSuffix class="input-icon">person</mat-icon>
            <mat-error *ngIf="registerForm.get('userName')?.hasError('required')">
              Le nom d'utilisateur est requis
            </mat-error>
          </mat-form-field>
  
          <mat-form-field appearance="outline" class="full-width" floatLabel="always">
            <mat-label>Email</mat-label>
            <input matInput type="email" formControlName="email" required>
            <mat-icon matSuffix class="input-icon">email</mat-icon>
            <mat-error *ngIf="registerForm.get('email')?.hasError('required')">
              L'email est requis
            </mat-error>
            <mat-error *ngIf="registerForm.get('email')?.hasError('email')">
              Format d'email invalide
            </mat-error>
          </mat-form-field>
  
          <mat-form-field appearance="outline" class="full-width" floatLabel="always">
            <mat-label>Nom complet</mat-label>
            <input matInput formControlName="fullName" required>
            <mat-icon matSuffix class="input-icon">badge</mat-icon>
            <mat-error *ngIf="registerForm.get('fullName')?.hasError('required')">
              Le nom complet est requis
            </mat-error>
          </mat-form-field>
  
          <mat-form-field appearance="outline" class="full-width" floatLabel="always">
            <mat-label>Mot de passe</mat-label>
            <input matInput type="password" formControlName="password" required>
            <mat-icon matSuffix class="input-icon">lock</mat-icon>
            <mat-error *ngIf="registerForm.get('password')?.hasError('required')">
              Le mot de passe est requis
            </mat-error>
            <mat-error *ngIf="registerForm.get('password')?.hasError('minlength')">
              Le mot de passe doit contenir au moins 8 caractères
            </mat-error>
          </mat-form-field>
  
          <mat-form-field appearance="outline" class="full-width" floatLabel="always">
            <mat-label>Nom de l'entreprise</mat-label>
            <input matInput formControlName="enterpriseName" required>
            <mat-icon matSuffix class="input-icon">business</mat-icon>
            <mat-error *ngIf="registerForm.get('enterpriseName')?.hasError('required')">
              Le nom de l'entreprise est requis
            </mat-error>
          </mat-form-field>
  
          <mat-form-field appearance="outline" class="full-width" floatLabel="always">
            <mat-label>Nombre d'employés</mat-label>
            <input matInput type="number" formControlName="numberOfEmployees" required>
            <mat-icon matSuffix class="input-icon">group</mat-icon>
            <mat-error *ngIf="registerForm.get('numberOfEmployees')?.hasError('required')">
              Le nombre d'employés est requis
            </mat-error>
          </mat-form-field>
  
          <mat-form-field appearance="outline" class="full-width" floatLabel="always">
            <mat-label>Date du contrat</mat-label>
            <input matInput type="date" formControlName="contractDate" required>
            <mat-icon matSuffix class="input-icon">calendar_today</mat-icon>
            <mat-error *ngIf="registerForm.get('contractDate')?.hasError('required')">
              La date du contrat est requise
            </mat-error>
          </mat-form-field>
  
          <mat-form-field appearance="outline" class="full-width" floatLabel="always">
            <mat-label>Catégorie</mat-label>
            <input matInput formControlName="category" required>
            <mat-icon matSuffix class="input-icon">category</mat-icon>
            <mat-error *ngIf="registerForm.get('category')?.hasError('required')">
              La catégorie est requise
            </mat-error>
          </mat-form-field>
  
          <div class="button-container">
            <button
              mat-raised-button
              color="primary"
              type="submit"
              class="submit-button"
              [disabled]="registerForm.invalid"
              [@buttonAnimation]
            >
              <mat-icon>business_center</mat-icon>
              Inscrire l'entreprise
            </button>
            <button
              mat-stroked-button
              color="warn"
              type="button"
              class="cancel-button"
              (click)="onCancel()"
              [@buttonAnimation]
            >
              <mat-icon>cancel</mat-icon>
              Annuler
            </button>
          </div>
        </form>
      </mat-card-content>
    </mat-card>
  </div>