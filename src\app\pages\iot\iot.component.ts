import { Component, Inject, OnInit } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialog, MatDialogRef } from '@angular/material/dialog';
import { MatSnackBar } from '@angular/material/snack-bar';
import { SensorDetails, SensorService } from '../../core/services/sensordetails.service';
import { AddSensorDialogComponent } from './add-sensor-dialog/add-sensor-dialog.component';
import { EditSensorDialogComponent } from './edit-sensor-dialog/edit-sensor-dialog.component';
import { CommonModule } from '@angular/common';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatDialogModule } from '@angular/material/dialog';
import { MatSnackBarModule } from '@angular/material/snack-bar';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { TableModule } from 'primeng/table';
import { ButtonModule } from 'primeng/button';
import { TooltipModule } from 'primeng/tooltip';
import { DropdownModule } from 'primeng/dropdown';

@Component({
  selector: 'app-iot',
  templateUrl: './iot.component.html',
  styleUrls: ['./iot.component.css'],
  standalone: true,
  imports: [
    CommonModule,
    MatIconModule,
    MatButtonModule,
    MatTooltipModule,
    MatDialogModule,
    MatSnackBarModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    TableModule,
    ButtonModule,
    TooltipModule,
    DropdownModule,
    FormsModule
  ]
})
export class IotComponent implements OnInit {
  sensors: SensorDetails[] = [];
  first: number = 0;
  rows: number = 5;

  constructor(
    private sensorService: SensorService,
    private dialog: MatDialog,
    private snackBar: MatSnackBar
  ) {}

  ngOnInit(): void {
    this.loadSensors();
  }

  loadSensors(): void {
    this.sensorService.getSensors().subscribe({
      next: (sensors) => {
        this.sensors = sensors;
      },
      error: (err) => {
        console.error('Error loading sensors', err);
        this.snackBar.open('Erreur lors du chargement des capteurs', 'Fermer', {
          duration: 3000,
          panelClass: ['error-snackbar']
        });
      }
    });
  }

  openAddSensorDialog(): void {
    const dialogRef = this.dialog.open(AddSensorDialogComponent, {
      width: '500px',
      disableClose: true
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.loadSensors();
        this.snackBar.open('Capteur ajouté avec succès !', 'Fermer', {
          duration: 3000,
          panelClass: ['success-snackbar']
        });
      }
    });
  }

  openEditSensorDialog(sensor: SensorDetails): void {
    const dialogRef = this.dialog.open(EditSensorDialogComponent, {
      width: '500px',
      disableClose: true,
      data: sensor
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result === 'deleted') {
        this.loadSensors();
        this.snackBar.open('Capteur supprimé avec succès !', 'Fermer', {
          duration: 3000,
          panelClass: ['success-snackbar']
        });
      } else if (result) {
        this.sensorService.updateSensor(result.id, result).subscribe({
          next: () => {
            this.loadSensors();
            this.snackBar.open('Capteur modifié avec succès !', 'Fermer', {
              duration: 3000,
              panelClass: ['success-snackbar']
            });
          },
          error: (err) => {
            console.error('Error updating sensor', err);
            this.snackBar.open('Erreur lors de la modification du capteur', 'Fermer', {
              duration: 3000,
              panelClass: ['error-snackbar']
            });
          }
        });
      }
    });
  }

    // Add this method to your component class
  onRowsPerPageChange(event: any): void {
    this.rows = event.value;
    this.first = 0; // Reset to first page
    this.pageChange({ first: 0, rows: event.value });
  }

  // Pagination controls
  next(): void {
    this.first = this.first + this.rows;
  }

  prev(): void {
    this.first = this.first - this.rows;
  }
  isFirstPage(): boolean {
    return this.first === 0;
  }

  isLastPage(): boolean {
    return this.first >= this.sensors.length - this.rows;
  }

  pageChange(event: any): void {
    this.first = event.first;
    this.rows = event.rows;
  }

  confirmDeleteSensor(sensor: SensorDetails): void {
    const confirmDialog = this.dialog.open(ConfirmDeleteDialogComponent, {
      width: '350px',
      data: { name: sensor.name }
    });
  
    confirmDialog.afterClosed().subscribe(result => {
      if (result) {
        this.sensorService.deleteSensor(sensor.id!).subscribe({
          next: () => {
            this.loadSensors();
            this.snackBar.open('Capteur supprimé avec succès !', 'Fermer', {
              duration: 3000,
              panelClass: ['success-snackbar']
            });
          },
          error: (err) => {
            console.error('Error deleting sensor', err);
            this.snackBar.open('Erreur lors de la suppression du capteur', 'Fermer', {
              duration: 3000,
              panelClass: ['error-snackbar']
            });
          }
        });
      }
    });
  }
  
}


@Component({
  selector: 'app-confirm-delete-dialog',
  template: `
    <div class="confirm-dialog">
      <h2 mat-dialog-title>Confirmer la Suppression</h2>
      <mat-dialog-content>
        Êtes-vous sûr de vouloir supprimer le capteur "{{ data.name }}" ?
      </mat-dialog-content>
      <mat-dialog-actions align="end">
        <button mat-raised-button color="warn" (click)="confirm()">Oui</button>
        <button mat-raised-button (click)="cancel()">Non</button>
      </mat-dialog-actions>
    </div>
  `,
  styles: [`
    .confirm-dialog {
      padding: 10px;
    }
    h2 {
      color: #d32f2f;
      font-size: 20px;
    }
    mat-dialog-content {
      font-size: 16px;
      color: #666;
    }
    mat-dialog-actions {
      gap: 10px;
    }
    button[mat-raised-button][color="warn"] {
      background-color: #4caf50;
      color: white;
    }
    button[mat-raised-button][color="warn"]:hover {
      background-color: #388e3c;
    }
    button[mat-raised-button] {
      background-color: #d32f2f;
      color: white;
    }
    button[mat-raised-button]:hover {
      background-color: #b71c1c;
    }
  `],
  standalone: true,
  imports: [
    MatDialogModule,
    MatButtonModule
  ]
})
export class ConfirmDeleteDialogComponent {
  constructor(
    public dialogRef: MatDialogRef<ConfirmDeleteDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: { name: string }
  ) {}

  confirm() {
    this.dialogRef.close(true);
  }

  cancel() {
    this.dialogRef.close(false);
  }
}
