/* Modern Login Styles with Advanced Effects */
.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #eef2f7 100%);
  padding: 1rem;
  font-family: var(--font-family);
  position: relative;
  overflow: hidden;
  perspective: 1000px;
}

/* Animated background */
.login-container::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 1;
  z-index: 0;
 
}

.login-card {
  width: 100%;
  max-width: 420px;
  background: var(--white);
  border-radius: var(--border-radius);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.07), 
              0 5px 15px var(--primary-shadow);
  overflow: hidden;
  padding: 2.5rem 2rem;
  border: 1px solid rgba(0, 0, 0, 0.05);
  position: relative;
  z-index: 1;
  transition: transform var(--transition-normal) cubic-bezier(0.34, 1.56, 0.64, 1),
              box-shadow var(--transition-normal) ease;
  transform-style: preserve-3d;
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.95);
}

.login-card:hover {
  transform: translateY(-5px) rotateX(2deg);
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.1), 
              0 15px 25px var(--primary-shadow);
}

/* Card header with logo */
.card-header {
  text-align: center;
  margin-bottom: 2.5rem;
  position: relative;
}

.logo-container {
  margin-bottom: 1.5rem;
  position: relative;
  display: inline-block;
}

.logo-container::after {
  content: "";
  position: absolute;
  width: 140%;
  height: 140%;
  top: -20%;
  left: -20%;
  background: radial-gradient(circle, rgba(76, 175, 80, 0.15) 0%, transparent 70%);
  opacity: 0.6;
  border-radius: 50%;
  z-index: -1;
  
}



.logo-circle {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 90px;
  height: 90px;
  border-radius: 15px;

}



.logo-circle::before {
  content: '';
  position: absolute;
  width: 100%;
  height: 100%;
  opacity: 0.1;
  z-index: -1;
  border-radius: inherit;
}


.logo-image {
  width: 85%;
  height: 85%;
  object-fit: contain;
  border-radius: 12px;
  filter: drop-shadow(0 4px 6px rgba(0, 0, 0, 0.1));
  transform-origin: center;
  transition: all var(--transition-normal) ease;
}

.logo-circle:hover {
  transform: translateY(-5px) scale(1.05);
  box-shadow: 0 12px 25px rgba(0, 0, 0, 0.15);
}

.logo-circle:hover .logo-image {
  transform: scale(1.1);
}

.card-subtitle {
  color: var(--text-secondary);
  font-size: 0.95rem;
  margin: 0;
  font-weight: 400;
  opacity: 0.85;
}

/* Modern input fields with centered icons */
.form-field {
  position: relative;
  margin-bottom: 1.75rem;
  transition: all var(--transition-normal) ease;
}

.p-float-label {
  display: block;
}

.p-float-label label {
  position: absolute;
  top: 1rem;
  left: 3.5rem;
  color: var(--text-secondary);
  font-weight: 400;
  pointer-events: none;
  transition: all var(--transition-fast) ease;
}

.custom-input {
  width: 100%;
  border-radius: 10px;
  padding: 1rem 1rem 1rem 3.5rem;
  border: 2px solid var(--grey-medium);
  transition: all var(--transition-normal) cubic-bezier(0.34, 1.56, 0.64, 1);
  background-color: var(--white);
  font-size: 0.95rem;
  color: var(--text-primary);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.custom-input:enabled:hover {
  border-color: var(--primary-light);
  box-shadow: 0 4px 12px rgba(76, 175, 80, 0.1);
}

.custom-input:enabled:focus {
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.2), 0 6px 16px rgba(76, 175, 80, 0.1);
  outline: none;
}

.input-icon {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: white;
  z-index: 2;
  font-size: 1.2rem;
  background: var(--primary-light);
  width: 2.2rem;
  height: 2.2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  transition: all var(--transition-normal) ease;
}

.form-field:focus-within .input-icon {
  background: var(--primary);
  transform: translateY(-50%) scale(1.1);
}

.form-field:focus-within label {
  color: var(--primary);
  transform: translateY(-1.2rem) scale(0.9);
  left: 3.5rem;
}

.error-message {
  display: block;
  color: var(--danger);
  font-size: 0.8rem;
  margin-top: 0.5rem;
  padding-left: 3.5rem;
}

/* Button styles */
.button-container {
  margin-top: 1.5rem;
}

.custom-button {
  width: 100%;
  padding: 1rem;
  font-size: 1rem;
  font-weight: 600;
  letter-spacing: 0.5px;
  text-transform: uppercase;
  background: linear-gradient(135deg, var(--primary), var(--primary-dark));
  border: none;
  border-radius: 10px;
  transition: all var(--transition-normal) cubic-bezier(0.34, 1.56, 0.64, 1);
  box-shadow: 0 4px 15px rgba(46, 125, 50, 0.3);
  color: white;
  position: relative;
  overflow: hidden;
}

.custom-button:enabled:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 20px rgba(46, 125, 50, 0.4);
  background: linear-gradient(135deg, var(--primary-dark), var(--primary));
}

.custom-button:enabled:active {
  transform: translateY(0);
  box-shadow: 0 4px 10px rgba(46, 125, 50, 0.3);
}

.custom-button::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  background: rgba(255, 255, 255, 0.5);
  opacity: 0;
  border-radius: 100%;
  transform: scale(1, 1) translate(-50%, -50%);
  transform-origin: 50% 50%;
}

.custom-button:focus:not(:active)::after {
  animation: ripple 0.6s ease-out;
}

@keyframes ripple {
  0% {
    transform: scale(0, 0);
    opacity: 0.5;
  }
  100% {
    transform: scale(20, 20);
    opacity: 0;
  }
}

.button-icon {
  font-size: 1.25rem;
  margin-right: 0.75rem;
  transition: transform var(--transition-normal) ease;
}

.custom-button:hover .button-icon {
  transform: translateX(3px);
}

/* Toast notifications */
:host ::ng-deep .p-toast {
  max-width: 25rem;
}

:host ::ng-deep .p-toast .p-toast-message {
  border-radius: 12px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  border: none;
  padding: 1.25rem 1.5rem;
}

:host ::ng-deep .p-toast .p-toast-message-success {
  background: linear-gradient(135deg, #E8F5E9, #F1F8E9);
  border-left: 4px solid var(--primary);
  color: var(--primary-dark);
}

:host ::ng-deep .p-toast .p-toast-message-success .p-toast-message-icon {
  color: var(--primary);
}

:host ::ng-deep .p-toast .p-toast-message-error {
  background: linear-gradient(135deg, #FFEBEE, #FFCDD2);
  border-left: 4px solid var(--danger);
  color: #C62828;
}

:host ::ng-deep .p-toast .p-toast-message-error .p-toast-message-icon {
  color: var(--danger);
}

:host ::ng-deep .p-toast .p-toast-message-content {
  align-items: flex-start;
}

:host ::ng-deep .p-toast .p-toast-icon-close {
  color: var(--text-secondary);
  transition: all var(--transition-fast) ease;
}

:host ::ng-deep .p-toast .p-toast-icon-close:hover {
  color: var(--text-primary);
  transform: rotate(90deg);
}

/* Responsive adjustments */
@media (max-width: 480px) {
  .login-card {
    padding: 2rem 1.5rem;
    border-radius: 10px;
  }
  
  .logo-circle {
    width: 80px;
    height: 80px;
  }
  
  .custom-input {
    padding-left: 3rem;
  }
  
  .input-icon {
    left: 1rem;
    width: 2rem;
    height: 2rem;
    font-size: 1.1rem;
  }
  
  .p-float-label label {
    left: 3rem;
  }
  
  .form-field:focus-within label {
    left: 3rem;
  }

  .error-message {
    padding-left: 3rem;
  }
}