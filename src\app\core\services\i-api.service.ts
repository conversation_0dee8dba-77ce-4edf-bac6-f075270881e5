import { Observable } from 'rxjs';

export interface IApiService<T> {

  getAll(endpoint?: string): Observable<T[]>;

  getOne(data: string): Observable<T>;

  getById(id: string): Observable<T>;

  post(endpoint: string, item: T): Observable<T>;

  put(endpoint: string, item: T): Observable<T>;

  create(item: T): Observable<T>;

  update(item: T): Observable<T>;

  delete(id: string): Observable<{ success: boolean }>; 

  count(): Observable<number>;
}
