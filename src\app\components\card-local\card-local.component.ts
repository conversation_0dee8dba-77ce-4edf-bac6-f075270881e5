import { Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { MatIconModule } from '@angular/material/icon';
import { LocalService } from '@app/core/services/local.service';
import { Local } from '@app/core/models/local';

@Component({
  selector: 'app-card-local',
  templateUrl: './card-local.component.html',
  styleUrls: ['./card-local.component.css'],
  standalone: true,
  imports: [CommonModule, MatIconModule],
})
export class CardLocalComponent {
  @Input() local!: Local;
  @Output() viewDetails = new EventEmitter<number>();
  @Output() editLocal = new EventEmitter<number>();
  @Output() deleteLocal = new EventEmitter<number>();

  imageArchitectureUrl: string = '';
  imageLocaleUrl: string = '';

  constructor(readonly router: Router, readonly localService: LocalService) {}
  localTypes = [
    { value: 'Salle de réunion', label: 'Salle de réunion' },
    { value: 'Bureau', label: 'Bureau' },
    { value: 'Laboratoire', label: 'Laboratoire' },
    { value: 'Amphithéâtre', label: 'Amphithéâtre' },
    { value: 'Espace détente', label: 'Espace détente' },
    { value: 'Salle de formation', label: 'Salle de formation' },
  ];

  ngOnInit() {
    // this.loadImages();
  }

  private loadImages() {
    // if (this.local.id) {
    //   // Load Architecture Image
    //   this.localService
    //     .getLocalImageArchitecture(this.local.id)
    //     .subscribe((blob) => {
    //       this.createImageFromBlob(blob, 'architecture');
    //     });

    //   // Load Locale Image
    //   this.localService.getLocalImageLocale(this.local.id).subscribe((blob) => {
    //     this.createImageFromBlob(blob, 'locale');
    //   });
    // }
  }

  private createImageFromBlob(blob: Blob, type: 'architecture' | 'locale') {
    // const reader = new FileReader();
    // reader.addEventListener('load', () => {
    //   if (type === 'architecture') {
    //     this.imageArchitectureUrl = reader.result as string;
    //   } else {
    //     this.imageLocaleUrl = reader.result as string;
    //   }
    // });
    // reader.readAsDataURL(blob);
  }
  onView(): void {
    // this.router.navigate(['site-details/', this.local.id]); // Navigate to the site details page with the local ID
    // this.viewDetails.emit(this.local.id);
  }
  getLocalTypeLabel(typeValue: string): string {
    // const type = this.localTypes.find((t) => t.value === typeValue);
    // return type ? type.label : typeValue;
    return ""
  }

  onEdit(): void {
    // this.editLocal.emit(this.local.id);
  }

  onDelete(): void {
    // this.deleteLocal.emit(this.local.id);
  }
}
